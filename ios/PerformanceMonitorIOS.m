// PerformanceMonitorIOS.m
#import "PerformanceMonitorIOS.h"
#import <React/RCTLog.h>
#import <mach/mach.h>
#import <mach/processor_info.h>
#import <mach/mach_host.h>
#import <sys/sysctl.h>
#import <sys/types.h>
#import <UIKit/UIKit.h>

@implementation PerformanceMonitorIOS

RCT_EXPORT_MODULE();

// Get current system metrics including accurate memory usage
RCT_EXPORT_METHOD(getCurrentMetrics:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        NSMutableDictionary *metrics = [NSMutableDictionary dictionary];
        
        // Memory metrics
        NSDictionary *memoryInfo = [self getMemoryInfo];
        [metrics setObject:memoryInfo forKey:@"memory"];
        
        // CPU metrics
        NSDictionary *cpuInfo = [self getCPUInfo];
        [metrics setObject:cpuInfo forKey:@"cpu"];
        
        // Process-specific metrics
        NSDictionary *processInfo = [self getProcessInfo];
        [metrics setObject:processInfo forKey:@"process"];
        
        // Energy metrics
        NSDictionary *energyInfo = [self getEnergyInfo];
        [metrics setObject:energyInfo forKey:@"energy"];
        
        resolve(metrics);
    } @catch (NSException *exception) {
        reject(@"METRICS_ERROR", exception.reason, nil);
    }
}

// Get detailed memory information including native allocations
- (NSDictionary *)getMemoryInfo {
    NSMutableDictionary *memoryInfo = [NSMutableDictionary dictionary];
    
    // System memory info
    mach_port_t host_port = mach_host_self();
    vm_size_t page_size;
    host_page_size(host_port, &page_size);
    
    vm_statistics64_data_t vm_stat;
    mach_msg_type_number_t host_size = sizeof(vm_statistics64_data_t) / sizeof(natural_t);
    
    if (host_statistics64(host_port, HOST_VM_INFO64, (host_info64_t)&vm_stat, &host_size) == KERN_SUCCESS) {
        uint64_t total_memory = (vm_stat.free_count + vm_stat.active_count + vm_stat.inactive_count + 
                                vm_stat.wire_count + vm_stat.compressor_page_count) * page_size;
        uint64_t free_memory = vm_stat.free_count * page_size;
        uint64_t used_memory = total_memory - free_memory;
        
        [memoryInfo setObject:@(total_memory / (1024 * 1024)) forKey:@"systemTotalMB"];
        [memoryInfo setObject:@(used_memory / (1024 * 1024)) forKey:@"systemUsedMB"];
        [memoryInfo setObject:@(free_memory / (1024 * 1024)) forKey:@"systemFreeMB"];
        [memoryInfo setObject:@(vm_stat.active_count * page_size / (1024 * 1024)) forKey:@"activeMB"];
        [memoryInfo setObject:@(vm_stat.inactive_count * page_size / (1024 * 1024)) forKey:@"inactiveMB"];
        [memoryInfo setObject:@(vm_stat.wire_count * page_size / (1024 * 1024)) forKey:@"wiredMB"];
        [memoryInfo setObject:@(vm_stat.compressor_page_count * page_size / (1024 * 1024)) forKey:@"compressedMB"];
    }
    
    // Process memory info
    struct task_basic_info info;
    mach_msg_type_number_t size = TASK_BASIC_INFO_COUNT;
    if (task_info(mach_task_self(), TASK_BASIC_INFO, (task_info_t)&info, &size) == KERN_SUCCESS) {
        [memoryInfo setObject:@(info.resident_size / (1024 * 1024)) forKey:@"processResidentMB"];
        [memoryInfo setObject:@(info.virtual_size / (1024 * 1024)) forKey:@"processVirtualMB"];
    }
    
    // Memory pressure
    dispatch_source_t memoryPressureSource = dispatch_source_create(DISPATCH_SOURCE_TYPE_MEMORYPRESSURE, 0, 
                                                                   DISPATCH_MEMORYPRESSURE_NORMAL | 
                                                                   DISPATCH_MEMORYPRESSURE_WARN | 
                                                                   DISPATCH_MEMORYPRESSURE_CRITICAL, 
                                                                   dispatch_get_main_queue());
    if (memoryPressureSource) {
        unsigned long pressureLevel = dispatch_source_get_data(memoryPressureSource);
        NSString *pressureString = @"normal";
        if (pressureLevel & DISPATCH_MEMORYPRESSURE_WARN) {
            pressureString = @"warning";
        } else if (pressureLevel & DISPATCH_MEMORYPRESSURE_CRITICAL) {
            pressureString = @"critical";
        }
        [memoryInfo setObject:pressureString forKey:@"memoryPressure"];
        dispatch_source_cancel(memoryPressureSource);
    }
    
    return memoryInfo;
}

// Get CPU information and usage
- (NSDictionary *)getCPUInfo {
    NSMutableDictionary *cpuInfo = [NSMutableDictionary dictionary];
    
    // CPU core count
    size_t size = sizeof(int);
    int cores;
    if (sysctlbyname("hw.ncpu", &cores, &size, NULL, 0) == 0) {
        [cpuInfo setObject:@(cores) forKey:@"cores"];
    }
    
    // CPU frequency
    uint64_t frequency;
    size = sizeof(frequency);
    if (sysctlbyname("hw.cpufrequency", &frequency, &size, NULL, 0) == 0) {
        [cpuInfo setObject:@(frequency / 1000000) forKey:@"frequencyMHz"];
    }
    
    // CPU usage
    host_cpu_load_info_data_t cpuinfo;
    mach_msg_type_number_t count = HOST_CPU_LOAD_INFO_COUNT;
    if (host_statistics(mach_host_self(), HOST_CPU_LOAD_INFO, (host_info_t)&cpuinfo, &count) == KERN_SUCCESS) {
        unsigned long totalTicks = cpuinfo.cpu_ticks[CPU_STATE_USER] + cpuinfo.cpu_ticks[CPU_STATE_SYSTEM] + 
                                  cpuinfo.cpu_ticks[CPU_STATE_IDLE] + cpuinfo.cpu_ticks[CPU_STATE_NICE];
        unsigned long usedTicks = cpuinfo.cpu_ticks[CPU_STATE_USER] + cpuinfo.cpu_ticks[CPU_STATE_SYSTEM] + 
                                 cpuinfo.cpu_ticks[CPU_STATE_NICE];
        
        if (totalTicks > 0) {
            double usage = (double)usedTicks / (double)totalTicks * 100.0;
            [cpuInfo setObject:@(usage) forKey:@"usage"];
        }
        
        [cpuInfo setObject:@(cpuinfo.cpu_ticks[CPU_STATE_USER]) forKey:@"userTicks"];
        [cpuInfo setObject:@(cpuinfo.cpu_ticks[CPU_STATE_SYSTEM]) forKey:@"systemTicks"];
        [cpuInfo setObject:@(cpuinfo.cpu_ticks[CPU_STATE_IDLE]) forKey:@"idleTicks"];
    }
    
    return cpuInfo;
}

// Get process-specific information
- (NSDictionary *)getProcessInfo {
    NSMutableDictionary *processInfo = [NSMutableDictionary dictionary];
    
    // Process ID
    [processInfo setObject:@([[NSProcessInfo processInfo] processIdentifier]) forKey:@"pid"];
    
    // Process name
    [processInfo setObject:[[NSProcessInfo processInfo] processName] forKey:@"name"];
    
    // Uptime
    NSTimeInterval uptime = [[NSProcessInfo processInfo] systemUptime];
    [processInfo setObject:@(uptime) forKey:@"systemUptime"];
    
    // Thread count
    thread_array_t thread_list;
    mach_msg_type_number_t thread_count;
    if (task_threads(mach_task_self(), &thread_list, &thread_count) == KERN_SUCCESS) {
        [processInfo setObject:@(thread_count) forKey:@"threadCount"];
        vm_deallocate(mach_task_self(), (vm_offset_t)thread_list, thread_count * sizeof(thread_t));
    }
    
    return processInfo;
}

// Get energy and battery information
- (NSDictionary *)getEnergyInfo {
    NSMutableDictionary *energyInfo = [NSMutableDictionary dictionary];
    
    // Battery level and state
    UIDevice *device = [UIDevice currentDevice];
    device.batteryMonitoringEnabled = YES;
    
    [energyInfo setObject:@(device.batteryLevel * 100) forKey:@"batteryLevel"];
    
    NSString *batteryState;
    switch (device.batteryState) {
        case UIDeviceBatteryStateUnknown:
            batteryState = @"unknown";
            break;
        case UIDeviceBatteryStateUnplugged:
            batteryState = @"unplugged";
            break;
        case UIDeviceBatteryStateCharging:
            batteryState = @"charging";
            break;
        case UIDeviceBatteryStateFull:
            batteryState = @"full";
            break;
    }
    [energyInfo setObject:batteryState forKey:@"batteryState"];
    
    // Low power mode
    if (@available(iOS 9.0, *)) {
        [energyInfo setObject:@([[NSProcessInfo processInfo] isLowPowerModeEnabled]) forKey:@"lowPowerMode"];
    }
    
    // Thermal state
    if (@available(iOS 11.0, *)) {
        NSString *thermalState;
        switch ([[NSProcessInfo processInfo] thermalState]) {
            case NSProcessInfoThermalStateNominal:
                thermalState = @"nominal";
                break;
            case NSProcessInfoThermalStateFair:
                thermalState = @"fair";
                break;
            case NSProcessInfoThermalStateSerious:
                thermalState = @"serious";
                break;
            case NSProcessInfoThermalStateCritical:
                thermalState = @"critical";
                break;
        }
        [energyInfo setObject:thermalState forKey:@"thermalState"];
    }
    
    return energyInfo;
}

@end
