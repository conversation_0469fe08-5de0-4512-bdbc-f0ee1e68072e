// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		21527C562E07E4D8000A3994 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 21527C502E07E4D8000A3994 /* AppDelegate.swift */; };
		21527C582E07E4D8000A3994 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 21527C532E07E4D8000A3994 /* LaunchScreen.storyboard */; };
		21527C592E07E4D8000A3994 /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 21527C512E07E4D8000A3994 /* Images.xcassets */; };
		21527C5A2E07E4D8000A3994 /* PrivacyInfo.xcprivacy in Resources */ = {isa = PBXBuildFile; fileRef = 21527C542E07E4D8000A3994 /* PrivacyInfo.xcprivacy */; };
		215AB9202E080DE600E4BF52 /* metadata_0007.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB90B2E080DE600E4BF52 /* metadata_0007.json */; };
		215AB9212E080DE600E4BF52 /* metadata_0006.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB90A2E080DE600E4BF52 /* metadata_0006.json */; };
		215AB9222E080DE600E4BF52 /* documents_hnsw_rs.hnsw.graph in Resources */ = {isa = PBXBuildFile; fileRef = 215AB91A2E080DE600E4BF52 /* documents_hnsw_rs.hnsw.graph */; };
		215AB9232E080DE600E4BF52 /* all-MiniLM-L6-v2.quant.onnx in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9182E080DE600E4BF52 /* all-MiniLM-L6-v2.quant.onnx */; };
		215AB9242E080DE600E4BF52 /* metadata_0018.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9162E080DE600E4BF52 /* metadata_0018.json */; };
		215AB9252E080DE600E4BF52 /* metadata_0002.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9062E080DE600E4BF52 /* metadata_0002.json */; };
		215AB9262E080DE600E4BF52 /* metadata_0010.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB90E2E080DE600E4BF52 /* metadata_0010.json */; };
		215AB9272E080DE600E4BF52 /* metadata_0015.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9132E080DE600E4BF52 /* metadata_0015.json */; };
		215AB9282E080DE600E4BF52 /* metadata_0008.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB90C2E080DE600E4BF52 /* metadata_0008.json */; };
		215AB9292E080DE600E4BF52 /* metadata_0014.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9122E080DE600E4BF52 /* metadata_0014.json */; };
		215AB92A2E080DE600E4BF52 /* metadata_index.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB91B2E080DE600E4BF52 /* metadata_index.json */; };
		215AB92B2E080DE600E4BF52 /* metadata_0012.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9102E080DE600E4BF52 /* metadata_0012.json */; };
		215AB92C2E080DE600E4BF52 /* metadata_0016.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9142E080DE600E4BF52 /* metadata_0016.json */; };
		215AB92D2E080DE600E4BF52 /* metadata_0011.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB90F2E080DE600E4BF52 /* metadata_0011.json */; };
		215AB92E2E080DE600E4BF52 /* tokenizer_config_rn.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB91D2E080DE600E4BF52 /* tokenizer_config_rn.json */; };
		215AB92F2E080DE600E4BF52 /* metadata_0003.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9072E080DE600E4BF52 /* metadata_0003.json */; };
		215AB9302E080DE600E4BF52 /* metadata_0013.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9112E080DE600E4BF52 /* metadata_0013.json */; };
		215AB9312E080DE600E4BF52 /* metadata_0004.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9082E080DE600E4BF52 /* metadata_0004.json */; };
		215AB9322E080DE600E4BF52 /* metadata_0005.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9092E080DE600E4BF52 /* metadata_0005.json */; };
		215AB9332E080DE600E4BF52 /* vocab.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB91E2E080DE600E4BF52 /* vocab.json */; };
		215AB9342E080DE600E4BF52 /* metadata_0009.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB90D2E080DE600E4BF52 /* metadata_0009.json */; };
		215AB9352E080DE600E4BF52 /* documents_hnsw_rs.hnsw.data in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9192E080DE600E4BF52 /* documents_hnsw_rs.hnsw.data */; };
		215AB9362E080DE600E4BF52 /* metadata_0000.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9042E080DE600E4BF52 /* metadata_0000.json */; };
		215AB9372E080DE600E4BF52 /* metadata_0017.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9152E080DE600E4BF52 /* metadata_0017.json */; };
		215AB9382E080DE600E4BF52 /* metadata_0001.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB9052E080DE600E4BF52 /* metadata_0001.json */; };
		215AB9392E080DE600E4BF52 /* test_example.json in Resources */ = {isa = PBXBuildFile; fileRef = 215AB91C2E080DE600E4BF52 /* test_example.json */; };
		21C709B82E02B88500E1969D /* libhnsw_lib.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3990CA4E2DD2392800355BDF /* libhnsw_lib.a */; };
		3990CA632DD2559700355BDF /* HnswSearchModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 3990CA612DD251D600355BDF /* HnswSearchModule.m */; };
		B9750D12DACA0D5DBFFA5460 /* libPods-SmartVA.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 1F13C0CC5EAC467AE98FEAD5 /* libPods-SmartVA.a */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0F86A2D1AF8590A882ADF1A6 /* Pods-SmartVA.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SmartVA.debug.xcconfig"; path = "Target Support Files/Pods-SmartVA/Pods-SmartVA.debug.xcconfig"; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* SmartVA.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SmartVA.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1F13C0CC5EAC467AE98FEAD5 /* libPods-SmartVA.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-SmartVA.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		21527C502E07E4D8000A3994 /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		21527C512E07E4D8000A3994 /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		21527C522E07E4D8000A3994 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		21527C532E07E4D8000A3994 /* LaunchScreen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		21527C542E07E4D8000A3994 /* PrivacyInfo.xcprivacy */ = {isa = PBXFileReference; lastKnownFileType = text.xml; path = PrivacyInfo.xcprivacy; sourceTree = "<group>"; };
		215AB9032E07EA5300E4BF52 /* Hnsw-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Hnsw-Bridging-Header.h"; sourceTree = "<group>"; };
		215AB9042E080DE600E4BF52 /* metadata_0000.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0000.json; sourceTree = "<group>"; };
		215AB9052E080DE600E4BF52 /* metadata_0001.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0001.json; sourceTree = "<group>"; };
		215AB9062E080DE600E4BF52 /* metadata_0002.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0002.json; sourceTree = "<group>"; };
		215AB9072E080DE600E4BF52 /* metadata_0003.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0003.json; sourceTree = "<group>"; };
		215AB9082E080DE600E4BF52 /* metadata_0004.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0004.json; sourceTree = "<group>"; };
		215AB9092E080DE600E4BF52 /* metadata_0005.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0005.json; sourceTree = "<group>"; };
		215AB90A2E080DE600E4BF52 /* metadata_0006.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0006.json; sourceTree = "<group>"; };
		215AB90B2E080DE600E4BF52 /* metadata_0007.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0007.json; sourceTree = "<group>"; };
		215AB90C2E080DE600E4BF52 /* metadata_0008.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0008.json; sourceTree = "<group>"; };
		215AB90D2E080DE600E4BF52 /* metadata_0009.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0009.json; sourceTree = "<group>"; };
		215AB90E2E080DE600E4BF52 /* metadata_0010.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0010.json; sourceTree = "<group>"; };
		215AB90F2E080DE600E4BF52 /* metadata_0011.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0011.json; sourceTree = "<group>"; };
		215AB9102E080DE600E4BF52 /* metadata_0012.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0012.json; sourceTree = "<group>"; };
		215AB9112E080DE600E4BF52 /* metadata_0013.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0013.json; sourceTree = "<group>"; };
		215AB9122E080DE600E4BF52 /* metadata_0014.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0014.json; sourceTree = "<group>"; };
		215AB9132E080DE600E4BF52 /* metadata_0015.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0015.json; sourceTree = "<group>"; };
		215AB9142E080DE600E4BF52 /* metadata_0016.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0016.json; sourceTree = "<group>"; };
		215AB9152E080DE600E4BF52 /* metadata_0017.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0017.json; sourceTree = "<group>"; };
		215AB9162E080DE600E4BF52 /* metadata_0018.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_0018.json; sourceTree = "<group>"; };
		215AB9182E080DE600E4BF52 /* all-MiniLM-L6-v2.quant.onnx */ = {isa = PBXFileReference; lastKnownFileType = file; path = "all-MiniLM-L6-v2.quant.onnx"; sourceTree = "<group>"; };
		215AB9192E080DE600E4BF52 /* documents_hnsw_rs.hnsw.data */ = {isa = PBXFileReference; lastKnownFileType = file; path = documents_hnsw_rs.hnsw.data; sourceTree = "<group>"; };
		215AB91A2E080DE600E4BF52 /* documents_hnsw_rs.hnsw.graph */ = {isa = PBXFileReference; lastKnownFileType = file; path = documents_hnsw_rs.hnsw.graph; sourceTree = "<group>"; };
		215AB91B2E080DE600E4BF52 /* metadata_index.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = metadata_index.json; sourceTree = "<group>"; };
		215AB91C2E080DE600E4BF52 /* test_example.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = test_example.json; sourceTree = "<group>"; };
		215AB91D2E080DE600E4BF52 /* tokenizer_config_rn.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = tokenizer_config_rn.json; sourceTree = "<group>"; };
		215AB91E2E080DE600E4BF52 /* vocab.json */ = {isa = PBXFileReference; lastKnownFileType = text.json; path = vocab.json; sourceTree = "<group>"; };
		3990CA4A2DD2286E00355BDF /* libhnsw_lib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; name = libhnsw_lib.a; path = lib/libhnsw_lib.a; sourceTree = "<group>"; };
		3990CA4E2DD2392800355BDF /* libhnsw_lib.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libhnsw_lib.a; sourceTree = "<group>"; };
		3990CA512DD2392800355BDF /* libhnsw_lib 2.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = "libhnsw_lib 2.a"; sourceTree = "<group>"; };
		3990CA5F2DD251D600355BDF /* HnswSearch.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HnswSearch.h; sourceTree = "<group>"; };
		3990CA602DD251D600355BDF /* HnswSearchModule.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = HnswSearchModule.h; sourceTree = "<group>"; };
		3990CA612DD251D600355BDF /* HnswSearchModule.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = HnswSearchModule.m; sourceTree = "<group>"; };
		5E4CFEC687C8B836FCD9681D /* Pods-EdgeLLM.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-EdgeLLM.debug.xcconfig"; path = "Target Support Files/Pods-EdgeLLM/Pods-EdgeLLM.debug.xcconfig"; sourceTree = "<group>"; };
		731B7B7B79CEA5C2716EA279 /* Pods-EdgeLLM.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-EdgeLLM.release.xcconfig"; path = "Target Support Files/Pods-EdgeLLM/Pods-EdgeLLM.release.xcconfig"; sourceTree = "<group>"; };
		A81ACFF0ACA29EBB57B7FCD1 /* Pods-SmartVA.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SmartVA.release.xcconfig"; path = "Target Support Files/Pods-SmartVA/Pods-SmartVA.release.xcconfig"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				21C709B82E02B88500E1969D /* libhnsw_lib.a in Frameworks */,
				B9750D12DACA0D5DBFFA5460 /* libPods-SmartVA.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		21527C552E07E4D8000A3994 /* SmartVA */ = {
			isa = PBXGroup;
			children = (
				21527C502E07E4D8000A3994 /* AppDelegate.swift */,
				21527C512E07E4D8000A3994 /* Images.xcassets */,
				21527C522E07E4D8000A3994 /* Info.plist */,
				21527C532E07E4D8000A3994 /* LaunchScreen.storyboard */,
				21527C542E07E4D8000A3994 /* PrivacyInfo.xcprivacy */,
			);
			path = SmartVA;
			sourceTree = "<group>";
		};
		215AB9172E080DE600E4BF52 /* metadata_chunks */ = {
			isa = PBXGroup;
			children = (
				215AB9042E080DE600E4BF52 /* metadata_0000.json */,
				215AB9052E080DE600E4BF52 /* metadata_0001.json */,
				215AB9062E080DE600E4BF52 /* metadata_0002.json */,
				215AB9072E080DE600E4BF52 /* metadata_0003.json */,
				215AB9082E080DE600E4BF52 /* metadata_0004.json */,
				215AB9092E080DE600E4BF52 /* metadata_0005.json */,
				215AB90A2E080DE600E4BF52 /* metadata_0006.json */,
				215AB90B2E080DE600E4BF52 /* metadata_0007.json */,
				215AB90C2E080DE600E4BF52 /* metadata_0008.json */,
				215AB90D2E080DE600E4BF52 /* metadata_0009.json */,
				215AB90E2E080DE600E4BF52 /* metadata_0010.json */,
				215AB90F2E080DE600E4BF52 /* metadata_0011.json */,
				215AB9102E080DE600E4BF52 /* metadata_0012.json */,
				215AB9112E080DE600E4BF52 /* metadata_0013.json */,
				215AB9122E080DE600E4BF52 /* metadata_0014.json */,
				215AB9132E080DE600E4BF52 /* metadata_0015.json */,
				215AB9142E080DE600E4BF52 /* metadata_0016.json */,
				215AB9152E080DE600E4BF52 /* metadata_0017.json */,
				215AB9162E080DE600E4BF52 /* metadata_0018.json */,
			);
			path = metadata_chunks;
			sourceTree = "<group>";
		};
		215AB91F2E080DE600E4BF52 /* assets */ = {
			isa = PBXGroup;
			children = (
				215AB9172E080DE600E4BF52 /* metadata_chunks */,
				215AB9182E080DE600E4BF52 /* all-MiniLM-L6-v2.quant.onnx */,
				215AB9192E080DE600E4BF52 /* documents_hnsw_rs.hnsw.data */,
				215AB91A2E080DE600E4BF52 /* documents_hnsw_rs.hnsw.graph */,
				215AB91B2E080DE600E4BF52 /* metadata_index.json */,
				215AB91C2E080DE600E4BF52 /* test_example.json */,
				215AB91D2E080DE600E4BF52 /* tokenizer_config_rn.json */,
				215AB91E2E080DE600E4BF52 /* vocab.json */,
			);
			path = assets;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				3990CA522DD2392800355BDF /* lib */,
				3990CA4A2DD2286E00355BDF /* libhnsw_lib.a */,
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				1F13C0CC5EAC467AE98FEAD5 /* libPods-SmartVA.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		3990CA4F2DD2392800355BDF /* device */ = {
			isa = PBXGroup;
			children = (
			);
			path = device;
			sourceTree = "<group>";
		};
		3990CA502DD2392800355BDF /* simulator */ = {
			isa = PBXGroup;
			children = (
				3990CA512DD2392800355BDF /* libhnsw_lib 2.a */,
			);
			path = simulator;
			sourceTree = "<group>";
		};
		3990CA522DD2392800355BDF /* lib */ = {
			isa = PBXGroup;
			children = (
				3990CA4F2DD2392800355BDF /* device */,
				3990CA502DD2392800355BDF /* simulator */,
				3990CA4E2DD2392800355BDF /* libhnsw_lib.a */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				215AB91F2E080DE600E4BF52 /* assets */,
				215AB9032E07EA5300E4BF52 /* Hnsw-Bridging-Header.h */,
				3990CA5F2DD251D600355BDF /* HnswSearch.h */,
				3990CA602DD251D600355BDF /* HnswSearchModule.h */,
				3990CA612DD251D600355BDF /* HnswSearchModule.m */,
				21527C552E07E4D8000A3994 /* SmartVA */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* SmartVA.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				5E4CFEC687C8B836FCD9681D /* Pods-EdgeLLM.debug.xcconfig */,
				731B7B7B79CEA5C2716EA279 /* Pods-EdgeLLM.release.xcconfig */,
				0F86A2D1AF8590A882ADF1A6 /* Pods-SmartVA.debug.xcconfig */,
				A81ACFF0ACA29EBB57B7FCD1 /* Pods-SmartVA.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		13B07F861A680F5B00A75B9A /* SmartVA */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "SmartVA" */;
			buildPhases = (
				18010F43630556B946CF052B /* [CP] Check Pods Manifest.lock */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				D8A5905DFDAD78D9181D472B /* [CP] Embed Pods Frameworks */,
				1C2DCA00D404D30CE76F1152 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SmartVA;
			productName = EdgeLLM;
			productReference = 13B07F961A680F5B00A75B9A /* SmartVA.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1610;
				TargetAttributes = {
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1630;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "SmartVA" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* SmartVA */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				21527C582E07E4D8000A3994 /* LaunchScreen.storyboard in Resources */,
				21527C592E07E4D8000A3994 /* Images.xcassets in Resources */,
				21527C5A2E07E4D8000A3994 /* PrivacyInfo.xcprivacy in Resources */,
				215AB9202E080DE600E4BF52 /* metadata_0007.json in Resources */,
				215AB9212E080DE600E4BF52 /* metadata_0006.json in Resources */,
				215AB9222E080DE600E4BF52 /* documents_hnsw_rs.hnsw.graph in Resources */,
				215AB9232E080DE600E4BF52 /* all-MiniLM-L6-v2.quant.onnx in Resources */,
				215AB9242E080DE600E4BF52 /* metadata_0018.json in Resources */,
				215AB9252E080DE600E4BF52 /* metadata_0002.json in Resources */,
				215AB9262E080DE600E4BF52 /* metadata_0010.json in Resources */,
				215AB9272E080DE600E4BF52 /* metadata_0015.json in Resources */,
				215AB9282E080DE600E4BF52 /* metadata_0008.json in Resources */,
				215AB9292E080DE600E4BF52 /* metadata_0014.json in Resources */,
				215AB92A2E080DE600E4BF52 /* metadata_index.json in Resources */,
				215AB92B2E080DE600E4BF52 /* metadata_0012.json in Resources */,
				215AB92C2E080DE600E4BF52 /* metadata_0016.json in Resources */,
				215AB92D2E080DE600E4BF52 /* metadata_0011.json in Resources */,
				215AB92E2E080DE600E4BF52 /* tokenizer_config_rn.json in Resources */,
				215AB92F2E080DE600E4BF52 /* metadata_0003.json in Resources */,
				215AB9302E080DE600E4BF52 /* metadata_0013.json in Resources */,
				215AB9312E080DE600E4BF52 /* metadata_0004.json in Resources */,
				215AB9322E080DE600E4BF52 /* metadata_0005.json in Resources */,
				215AB9332E080DE600E4BF52 /* vocab.json in Resources */,
				215AB9342E080DE600E4BF52 /* metadata_0009.json in Resources */,
				215AB9352E080DE600E4BF52 /* documents_hnsw_rs.hnsw.data in Resources */,
				215AB9362E080DE600E4BF52 /* metadata_0000.json in Resources */,
				215AB9372E080DE600E4BF52 /* metadata_0017.json in Resources */,
				215AB9382E080DE600E4BF52 /* metadata_0001.json in Resources */,
				215AB9392E080DE600E4BF52 /* test_example.json in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"$REACT_NATIVE_PATH/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"$REACT_NATIVE_PATH/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		18010F43630556B946CF052B /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SmartVA-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		1C2DCA00D404D30CE76F1152 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SmartVA/Pods-SmartVA-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SmartVA/Pods-SmartVA-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SmartVA/Pods-SmartVA-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		D8A5905DFDAD78D9181D472B /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SmartVA/Pods-SmartVA-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SmartVA/Pods-SmartVA-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SmartVA/Pods-SmartVA-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				21527C562E07E4D8000A3994 /* AppDelegate.swift in Sources */,
				3990CA632DD2559700355BDF /* HnswSearchModule.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0F86A2D1AF8590A882ADF1A6 /* Pods-SmartVA.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JBVWT8C5GS;
				ENABLE_BITCODE = NO;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTDeprecation\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-Fabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricComponents\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricImage\"",
					"\"${PODS_ROOT}/Headers/Public/React-ImageManager\"",
					"\"${PODS_ROOT}/Headers/Public/React-Mapbuffer\"",
					"\"${PODS_ROOT}/Headers/Public/React-NativeModulesApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAppDelegate\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeCore\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeHermes\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-debug\"",
					"\"${PODS_ROOT}/Headers/Public/React-defaultsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-domnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflags\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-graphics\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-jserrorhandler\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-microtasksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-nativeconfig\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-performancetimeline\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererconsistency\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererdebug\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimescheduler\"",
					"\"${PODS_ROOT}/Headers/Public/React-timing\"",
					"\"${PODS_ROOT}/Headers/Public/React-utils\"",
					"\"${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCodegen\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/fast_float\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"${PODS_ROOT}/Headers/Public/llama-rn\"",
					"\"$(PROJECT_DIR)\"",
					"\"${PODS_ROOT}/Headers/Public/onnxruntime-c\"",
					"\"${PODS_ROOT}/Headers/Public/onnxruntime-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webassembly\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/Headers/Private/Yoga\"",
				);
				INFOPLIST_FILE = SmartVA/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/lib",
					"$(PROJECT_DIR)/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lhnsw_lib",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.brandon.SmartVA-";
				PRODUCT_NAME = SmartVA;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Hnsw-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A81ACFF0ACA29EBB57B7FCD1 /* Pods-SmartVA.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = JBVWT8C5GS;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"\"${PODS_ROOT}/Headers/Public\"",
					"\"${PODS_ROOT}/Headers/Public/DoubleConversion\"",
					"\"${PODS_ROOT}/Headers/Public/FBLazyVector\"",
					"\"${PODS_ROOT}/Headers/Public/RCT-Folly\"",
					"\"${PODS_ROOT}/Headers/Public/RCTDeprecation\"",
					"\"${PODS_ROOT}/Headers/Public/RCTRequired\"",
					"\"${PODS_ROOT}/Headers/Public/RCTTypeSafety\"",
					"\"${PODS_ROOT}/Headers/Public/RNFS\"",
					"\"${PODS_ROOT}/Headers/Public/RNVectorIcons\"",
					"\"${PODS_ROOT}/Headers/Public/React-Core\"",
					"\"${PODS_ROOT}/Headers/Public/React-Fabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricComponents\"",
					"\"${PODS_ROOT}/Headers/Public/React-FabricImage\"",
					"\"${PODS_ROOT}/Headers/Public/React-ImageManager\"",
					"\"${PODS_ROOT}/Headers/Public/React-Mapbuffer\"",
					"\"${PODS_ROOT}/Headers/Public/React-NativeModulesApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAnimation\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTAppDelegate\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTBlob\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFBReactNativeSpec\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTFabric\"",
					"\"${PODS_ROOT}/Headers/Public/React-RCTText\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeApple\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeCore\"",
					"\"${PODS_ROOT}/Headers/Public/React-RuntimeHermes\"",
					"\"${PODS_ROOT}/Headers/Public/React-callinvoker\"",
					"\"${PODS_ROOT}/Headers/Public/React-cxxreact\"",
					"\"${PODS_ROOT}/Headers/Public/React-debug\"",
					"\"${PODS_ROOT}/Headers/Public/React-defaultsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-domnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflags\"",
					"\"${PODS_ROOT}/Headers/Public/React-featureflagsnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-graphics\"",
					"\"${PODS_ROOT}/Headers/Public/React-hermes\"",
					"\"${PODS_ROOT}/Headers/Public/React-idlecallbacksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-jserrorhandler\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsi\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsiexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-jsinspector\"",
					"\"${PODS_ROOT}/Headers/Public/React-logger\"",
					"\"${PODS_ROOT}/Headers/Public/React-microtasksnativemodule\"",
					"\"${PODS_ROOT}/Headers/Public/React-nativeconfig\"",
					"\"${PODS_ROOT}/Headers/Public/React-perflogger\"",
					"\"${PODS_ROOT}/Headers/Public/React-performancetimeline\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererconsistency\"",
					"\"${PODS_ROOT}/Headers/Public/React-rendererdebug\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimeexecutor\"",
					"\"${PODS_ROOT}/Headers/Public/React-runtimescheduler\"",
					"\"${PODS_ROOT}/Headers/Public/React-timing\"",
					"\"${PODS_ROOT}/Headers/Public/React-utils\"",
					"\"${PODS_ROOT}/Headers/Public/ReactAppDependencyProvider\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCodegen\"",
					"\"${PODS_ROOT}/Headers/Public/ReactCommon\"",
					"\"${PODS_ROOT}/Headers/Public/SocketRocket\"",
					"\"${PODS_ROOT}/Headers/Public/Yoga\"",
					"\"${PODS_ROOT}/Headers/Public/boost\"",
					"\"${PODS_ROOT}/Headers/Public/fast_float\"",
					"\"${PODS_ROOT}/Headers/Public/fmt\"",
					"\"${PODS_ROOT}/Headers/Public/glog\"",
					"\"${PODS_ROOT}/Headers/Public/hermes-engine\"",
					"\"${PODS_ROOT}/Headers/Public/llama-rn\"",
					"\"$(PROJECT_DIR)\"",
					"\"${PODS_ROOT}/Headers/Public/onnxruntime-c\"",
					"\"${PODS_ROOT}/Headers/Public/onnxruntime-react-native\"",
					"\"${PODS_ROOT}/Headers/Public/react-native-webassembly\"",
					"\"$(PODS_ROOT)/DoubleConversion\"",
					"\"$(PODS_ROOT)/boost\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/Headers/Private/React-Core\"",
					"\"$(PODS_ROOT)/Headers/Private/Yoga\"",
				);
				INFOPLIST_FILE = SmartVA/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(SDKROOT)/usr/lib/swift",
					"$(inherited)",
					"$(PROJECT_DIR)/lib",
					"$(PROJECT_DIR)/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
					"-lhnsw_lib",
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.brandon.SmartVA-";
				PRODUCT_NAME = SmartVA;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "$(PROJECT_DIR)/Hnsw-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "$(inherited) DEBUG";
				USE_HERMES = true;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++20";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.1;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
					"-DFOLLY_CFG_NO_COROUTINES=1",
					"-DFOLLY_HAVE_CLOCK_GETTIME=1",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					" ",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				USE_HERMES = true;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "SmartVA" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "SmartVA" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
