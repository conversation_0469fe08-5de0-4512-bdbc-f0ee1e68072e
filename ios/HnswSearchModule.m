// HnswSearchModule.m
#import "HnswSearchModule.h"
#import "HnswSearch.h"
#import <React/RCTLog.h>

@implementation HnswSearchModule

RCT_EXPORT_MODULE();

RCT_EXPORT_METHOD(searchKnn:(NSArray *)queryVector 
                  dimension:(NSInteger)dim
                  k:(NSInteger)k
                  indexPath:(NSString *)indexPath
                  baseFilename:(NSString *)baseFilename
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        if (!indexPath || !baseFilename) {
        reject(@"INVALID_INPUT", @"Index path or base filename is nil", nil);
        return;
        }

        if ([queryVector count] < dim) {
            reject(@"INVALID_INPUT", @"Query vector length is less than dimension", nil);
            return;
        }
        
        // Convert JavaScript array to float array
        float query[dim];
        for (int i = 0; i < dim; i++) {
            query[i] = [queryVector[i] floatValue];
        }
        
        // Prepare output buffer
        int results[k];
        
        // Call the Rust function
        int status = hnsw_search(
            query, 
            (size_t)dim, 
            (size_t)k, 
            results, 
            [indexPath UTF8String], 
            [baseFilename UTF8String]
        );
        
        if (status != 0) {
            reject(@"HNSW_ERROR", @"Failed to search the index", nil);
            return;
        }
        
        // Convert results to JavaScript array
        NSMutableArray *resultArray = [NSMutableArray arrayWithCapacity:k];
        NSMutableArray *distanceArray = [NSMutableArray arrayWithCapacity:k]; // This will be empty as we don't return distances
        
        for (int i = 0; i < k; i++) {
            [resultArray addObject:@(results[i])];
            [distanceArray addObject:@(0.0f)]; // Placeholder for distance
        }
        
        // Return object with neighbors and distances arrays
        NSDictionary *returnObject = @{
            @"neighbors": resultArray,
            @"distances": distanceArray
        };
        
        resolve(returnObject);
    } @catch (NSException *exception) {
        reject(@"EXCEPTION", exception.reason, nil);
    }
}

@end