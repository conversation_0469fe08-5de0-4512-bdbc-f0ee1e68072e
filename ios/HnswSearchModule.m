// HnswSearchModule.m
#import "HnswSearchModule.h"
#import "HnswSearch.h"
#import <React/RCTLog.h>

@implementation HnswSearchModule

RCT_EXPORT_MODULE();

RCT_EXPORT_METHOD(searchKnn:(NSArray *)queryVector 
                  dimension:(NSInteger)dim
                  k:(NSInteger)k
                  indexPath:(NSString *)indexPath
                  baseFilename:(NSString *)baseFilename
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        if (!indexPath || !baseFilename) {
        reject(@"INVALID_INPUT", @"Index path or base filename is nil", nil);
        return;
        }

        if ([queryVector count] < dim) {
            reject(@"INVALID_INPUT", @"Query vector length is less than dimension", nil);
            return;
        }
        
        // Convert JavaScript array to float array
        float query[dim];
        for (int i = 0; i < dim; i++) {
            query[i] = [queryVector[i] floatValue];
        }
        
        // Prepare output buffer
        int results[k];
        
        // Call the Rust function
        int status = hnsw_search(
            query, 
            (size_t)dim, 
            (size_t)k, 
            results, 
            [indexPath UTF8String], 
            [baseFilename UTF8String]
        );
        
        if (status != 0) {
            reject(@"HNSW_ERROR", @"Failed to search the index", nil);
            return;
        }
        
        // Convert results to JavaScript array
        NSMutableArray *resultArray = [NSMutableArray arrayWithCapacity:k];
        NSMutableArray *distanceArray = [NSMutableArray arrayWithCapacity:k]; // This will be empty as we don't return distances
        
        for (int i = 0; i < k; i++) {
            [resultArray addObject:@(results[i])];
            [distanceArray addObject:@(0.0f)]; // Placeholder for distance
        }
        
        // Return object with neighbors and distances arrays
        NSDictionary *returnObject = @{
            @"neighbors": resultArray,
            @"distances": distanceArray
        };
        
        resolve(returnObject);
    } @catch (NSException *exception) {
        reject(@"EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(buildIndex:(NSArray *)embeddings
                  numDocs:(NSInteger)numDocs
                  dimension:(NSInteger)dim
                  indexPath:(NSString *)indexPath
                  baseFilename:(NSString *)baseFilename
                  m:(NSInteger)m
                  efConstruction:(NSInteger)efConstruction
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        if (!indexPath || !baseFilename) {
            reject(@"INVALID_INPUT", @"Index path or base filename is nil", nil);
            return;
        }

        if ([embeddings count] != numDocs * dim) {
            reject(@"INVALID_INPUT", @"Embeddings array size doesn't match numDocs * dim", nil);
            return;
        }

        // Convert JavaScript array to float array
        float *embeddingArray = malloc(sizeof(float) * numDocs * dim);
        for (int i = 0; i < numDocs * dim; i++) {
            embeddingArray[i] = [embeddings[i] floatValue];
        }

        // Call the Rust function
        int status = hnsw_build_index(
            embeddingArray,
            (size_t)numDocs,
            (size_t)dim,
            [indexPath UTF8String],
            [baseFilename UTF8String],
            (size_t)m,
            (size_t)efConstruction
        );

        free(embeddingArray);

        if (status != 0) {
            reject(@"HNSW_ERROR", @"Failed to build the index", nil);
            return;
        }

        resolve(@{@"success": @YES, @"message": @"Index built successfully"});
    } @catch (NSException *exception) {
        reject(@"EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(addToIndex:(NSArray *)embeddings
                  numNewDocs:(NSInteger)numNewDocs
                  dimension:(NSInteger)dim
                  indexPath:(NSString *)indexPath
                  baseFilename:(NSString *)baseFilename
                  startId:(NSInteger)startId
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        if (!indexPath || !baseFilename) {
            reject(@"INVALID_INPUT", @"Index path or base filename is nil", nil);
            return;
        }

        if ([embeddings count] != numNewDocs * dim) {
            reject(@"INVALID_INPUT", @"Embeddings array size doesn't match numNewDocs * dim", nil);
            return;
        }

        // Convert JavaScript array to float array
        float *embeddingArray = malloc(sizeof(float) * numNewDocs * dim);
        for (int i = 0; i < numNewDocs * dim; i++) {
            embeddingArray[i] = [embeddings[i] floatValue];
        }

        // Call the Rust function
        int status = hnsw_add_to_index(
            embeddingArray,
            (size_t)numNewDocs,
            (size_t)dim,
            [indexPath UTF8String],
            [baseFilename UTF8String],
            (size_t)startId
        );

        free(embeddingArray);

        if (status != 0) {
            reject(@"HNSW_ERROR", @"Failed to add to index", nil);
            return;
        }

        resolve(@{@"success": @YES, @"message": @"Documents added to index successfully"});
    } @catch (NSException *exception) {
        reject(@"EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(getDocumentCount:(NSString *)indexPath
                  baseFilename:(NSString *)baseFilename
                  resolver:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        if (!indexPath || !baseFilename) {
            reject(@"INVALID_INPUT", @"Index path or base filename is nil", nil);
            return;
        }

        // Call the Rust function
        int count = hnsw_get_doc_count(
            [indexPath UTF8String],
            [baseFilename UTF8String]
        );

        if (count < 0) {
            reject(@"HNSW_ERROR", @"Failed to get document count", nil);
            return;
        }

        resolve(@{@"count": @(count)});
    } @catch (NSException *exception) {
        reject(@"EXCEPTION", exception.reason, nil);
    }
}

RCT_EXPORT_METHOD(clearCache:(RCTPromiseResolveBlock)resolve
                  rejecter:(RCTPromiseRejectBlock)reject)
{
    @try {
        int status = hnsw_clear_cache();

        if (status != 0) {
            reject(@"HNSW_ERROR", @"Failed to clear cache", nil);
            return;
        }

        resolve(@{@"success": @YES, @"message": @"Cache cleared successfully"});
    } @catch (NSException *exception) {
        reject(@"EXCEPTION", exception.reason, nil);
    }
}

@end