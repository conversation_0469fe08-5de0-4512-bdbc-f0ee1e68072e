// HnswSearch.h
#ifndef HnswSearch_h
#define HnswSearch_h

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

int hnsw_search(
    const float* query_ptr,
    size_t dim,
    size_t k,
    int* out_ptr,
    const char* dir_ptr,
    const char* base_ptr
);

int hnsw_build_index(
    const float* embeddings_ptr,
    size_t num_docs,
    size_t dim,
    const char* dir_ptr,
    const char* base_ptr,
    size_t m,
    size_t ef_construction
);

int hnsw_add_to_index(
    const float* embeddings_ptr,
    size_t num_new_docs,
    size_t dim,
    const char* dir_ptr,
    const char* base_ptr,
    size_t start_id
);

int hnsw_get_doc_count(
    const char* dir_ptr,
    const char* base_ptr
);

int hnsw_clear_cache(void);

#ifdef __cplusplus
}
#endif

#endif /* HnswSearch_h */