// src/services/EnhancedRAGService.js - Enhanced RAG service with dynamic index building
import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { NativeModules } from 'react-native';
import RAGService from './RAGService';

const { HnswSearchModule } = NativeModules;

class EnhancedRAGService {
  constructor() {
    this.baseRAGService = RAGService;
    this.userIndexPath = `${RNFS.DocumentDirectoryPath}/user_documents_hnsw_rs`;
    this.userMetadataPath = `${RNFS.DocumentDirectoryPath}/user_metadata.json`;
    this.userDocuments = new Map();
    this.nextDocumentId = 0;
    this.isUserIndexInitialized = false;
  }

  // Initialize the enhanced RAG service
  async initialize() {
    // First initialize the base RAG service
    const baseInitialized = await this.baseRAGService.initialize();
    if (!baseInitialized) {
      return false;
    }

    // Load existing user documents if any
    await this.loadUserDocuments();

    // Get the next available document ID
    await this.initializeDocumentId();

    return true;
  }

  // Load existing user documents from metadata file
  async loadUserDocuments() {
    try {
      const exists = await RNFS.exists(this.userMetadataPath);
      if (exists) {
        const metadataContent = await RNFS.readFile(this.userMetadataPath, 'utf8');
        const metadata = JSON.parse(metadataContent);
        
        // Restore user documents map
        this.userDocuments.clear();
        metadata.documents.forEach(doc => {
          this.userDocuments.set(doc.id, doc);
        });

        this.isUserIndexInitialized = metadata.indexInitialized || false;
        console.log(`📄 Loaded ${this.userDocuments.size} user documents`);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load user documents:', error);
      this.userDocuments.clear();
      this.isUserIndexInitialized = false;
    }
  }

  // Save user documents metadata to file
  async saveUserDocuments() {
    try {
      const metadata = {
        documents: Array.from(this.userDocuments.values()),
        indexInitialized: this.isUserIndexInitialized,
        lastUpdated: new Date().toISOString()
      };

      await RNFS.writeFile(this.userMetadataPath, JSON.stringify(metadata, null, 2), 'utf8');
      console.log(`💾 Saved ${this.userDocuments.size} user documents to metadata`);
    } catch (error) {
      console.error('❌ Failed to save user documents:', error);
      throw error;
    }
  }

  // Initialize the next document ID based on existing indices
  async initializeDocumentId() {
    try {
      // Get count from main index
      const mainCount = this.baseRAGService.getStats().totalDocuments || 0;
      
      // Get count from user index if it exists
      let userCount = 0;
      if (this.isUserIndexInitialized) {
        try {
          const result = await HnswSearchModule.getDocumentCount(
            RNFS.DocumentDirectoryPath,
            'user_documents_hnsw_rs'
          );
          userCount = result.count || 0;
        } catch (error) {
          console.warn('⚠️ Failed to get user index count:', error);
        }
      }

      // Set next ID to be after both indices
      this.nextDocumentId = Math.max(mainCount, userCount) + 1000; // Add buffer
      console.log(`🔢 Next document ID set to: ${this.nextDocumentId}`);
    } catch (error) {
      console.warn('⚠️ Failed to initialize document ID:', error);
      this.nextDocumentId = 100000; // Fallback to high number
    }
  }

  // Add a new document with native index building
  async addDocument(documentData) {
    try {
      console.log(`📄 Adding document with native indexing: ${documentData.title || 'Untitled'}`);

      // Generate embedding using the base service
      const embedding = await this.baseRAGService.embedText(documentData.content);
      
      // Create document metadata
      const docId = this.nextDocumentId++;
      const docMetadata = {
        id: docId,
        title: documentData.title || 'User Document',
        content: documentData.content,
        source: documentData.source || 'user_upload',
        type: documentData.type || 'text',
        timestamp: new Date().toISOString(),
        embedding: Array.from(embedding)
      };

      // Store in user documents map
      this.userDocuments.set(docId, docMetadata);

      // Build or update the native HNSW index
      await this.updateNativeIndex([docMetadata]);

      // Save metadata to disk
      await this.saveUserDocuments();

      console.log(`✅ Document added with native indexing: ${docId}`);
      
      return {
        success: true,
        documentId: docId,
        message: "Document added to native RAG index"
      };

    } catch (error) {
      console.error(`❌ Failed to add document with native indexing:`, error);
      throw error;
    }
  }

  // Update the native HNSW index with new documents
  async updateNativeIndex(newDocuments) {
    try {
      if (!newDocuments || newDocuments.length === 0) {
        return;
      }

      const embeddings = [];
      newDocuments.forEach(doc => {
        embeddings.push(...doc.embedding);
      });

      const dimension = newDocuments[0].embedding.length;

      if (!this.isUserIndexInitialized) {
        // Build new index
        console.log(`🔨 Building new user index with ${newDocuments.length} documents`);
        
        await HnswSearchModule.buildIndex(
          embeddings,
          newDocuments.length,
          dimension,
          RNFS.DocumentDirectoryPath,
          'user_documents_hnsw_rs',
          16, // m parameter
          200 // ef_construction parameter
        );

        this.isUserIndexInitialized = true;
      } else {
        // Add to existing index
        console.log(`➕ Adding ${newDocuments.length} documents to existing user index`);
        
        const startId = newDocuments[0].id;
        
        await HnswSearchModule.addToIndex(
          embeddings,
          newDocuments.length,
          dimension,
          RNFS.DocumentDirectoryPath,
          'user_documents_hnsw_rs',
          startId
        );
      }

      console.log(`✅ Native index updated successfully`);
    } catch (error) {
      console.error('❌ Failed to update native index:', error);
      throw error;
    }
  }

  // Search both main and user indices
  async querySimilarDocuments(query, k = 5) {
    const startTime = Date.now();

    // Generate embedding
    const embedding = await this.baseRAGService.embedText(query);

    // Search main index
    const mainResults = await this.baseRAGService.querySimilarDocuments(query, k);
    
    // Search user index if it exists
    let userResults = [];
    if (this.isUserIndexInitialized && this.userDocuments.size > 0) {
      try {
        const searchResult = await HnswSearchModule.searchKnn(
          Array.from(embedding),
          embedding.length,
          Math.min(k, this.userDocuments.size),
          RNFS.DocumentDirectoryPath,
          'user_documents_hnsw_rs'
        );

        // Convert search results to document objects
        userResults = searchResult.neighbors.map((docId, index) => {
          const doc = this.userDocuments.get(docId);
          if (doc) {
            return {
              id: doc.id,
              title: doc.title,
              content: doc.content,
              source: doc.source,
              type: doc.type,
              timestamp: doc.timestamp,
              distance: searchResult.distances[index],
              score: 1 - searchResult.distances[index] // Convert distance to similarity
            };
          }
          return null;
        }).filter(doc => doc !== null);

        console.log(`📄 Found ${userResults.length} relevant user documents`);
      } catch (error) {
        console.warn("⚠️ Failed to search user index:", error);
      }
    }

    // Combine and sort results
    const allDocs = [...mainResults.documents, ...userResults];
    const finalDocs = allDocs
      .sort((a, b) => (b.score || (1 - (b.distance || 0))) - (a.score || (1 - (a.distance || 0))))
      .slice(0, k);

    const queryTime = Date.now() - startTime;

    console.log(`🎯 Enhanced RAG query completed in ${queryTime}ms: ${finalDocs.length}/${k} docs (${mainResults.documents.length} main, ${userResults.length} user)`);

    return {
      documents: finalDocs,
      queryTime,
      cacheStats: mainResults.cacheStats
    };
  }

  // Remove user document
  async removeUserDocument(documentId) {
    if (!this.userDocuments.has(documentId)) {
      throw new Error(`Document ${documentId} not found`);
    }

    this.userDocuments.delete(documentId);
    
    // Save updated metadata
    await this.saveUserDocuments();

    // Note: We don't rebuild the index here for performance reasons
    // The document will still exist in the index but won't be returned in results
    // A full rebuild could be triggered periodically or on demand

    console.log(`🗑️ Removed user document: ${documentId}`);
    
    return {
      success: true,
      message: "Document removed successfully"
    };
  }

  // Get enhanced statistics
  getStats() {
    const baseStats = this.baseRAGService.getStats();
    return {
      ...baseStats,
      userDocuments: this.userDocuments.size,
      userIndexInitialized: this.isUserIndexInitialized,
      nextDocumentId: this.nextDocumentId,
      totalDocumentsIncludingUser: baseStats.totalDocuments + this.userDocuments.size
    };
  }

  // Get user documents list
  getUserDocuments() {
    return Array.from(this.userDocuments.values()).map(doc => ({
      id: doc.id,
      title: doc.title,
      source: doc.source,
      type: doc.type,
      timestamp: doc.timestamp,
      contentLength: doc.content.length,
      contentPreview: doc.content.substring(0, 100) + (doc.content.length > 100 ? '...' : '')
    }));
  }

  // Clear all caches
  clearCaches() {
    this.baseRAGService.clearCaches();
    
    // Clear native cache
    if (HnswSearchModule.clearCache) {
      HnswSearchModule.clearCache().catch(error => {
        console.warn('⚠️ Failed to clear native cache:', error);
      });
    }
  }

  // Delegate other methods to base service
  async testTermEmbedding(term) {
    return this.baseRAGService.testTermEmbedding(term);
  }

  async runDiagnostics() {
    const baseDiagnostics = await this.baseRAGService.runDiagnostics();
    
    // Add user index diagnostics
    baseDiagnostics.results.userDocuments = `${this.userDocuments.size} documents`;
    baseDiagnostics.results.userIndexInitialized = this.isUserIndexInitialized ? "YES" : "NO";
    
    if (this.isUserIndexInitialized) {
      try {
        const result = await HnswSearchModule.getDocumentCount(
          RNFS.DocumentDirectoryPath,
          'user_documents_hnsw_rs'
        );
        baseDiagnostics.results.userIndexCount = `${result.count} documents in native index`;
      } catch (error) {
        baseDiagnostics.results.userIndexCount = `FAILED - ${error.message}`;
      }
    }

    return baseDiagnostics;
  }
}

// Export singleton instance
export default new EnhancedRAGService();
