// src/services/PerformanceMonitor.js - Cross-platform performance monitoring
import { Platform, NativeModules } from 'react-native';
import DeviceInfo from 'react-native-device-info';

class PerformanceMonitor {
  constructor() {
    this.isMonitoring = false;
    this.monitoringInterval = null;
    this.performanceData = [];
    this.baselineMemory = null;
    this.callbacks = new Set();
    this.lastCpuUsage = null;
    this.energyBaseline = null;
    
    // Platform-specific modules (will be created next)
    this.nativeModule = Platform.OS === 'ios' 
      ? NativeModules.PerformanceMonitorIOS 
      : NativeModules.PerformanceMonitorAndroid;
  }

  // Initialize the performance monitor
  async initialize() {
    try {
      console.log('🔧 Initializing PerformanceMonitor...');
      
      // Get baseline system information
      const deviceInfo = await this.getDeviceInfo();
      console.log('📱 Device Info:', deviceInfo);
      
      // Establish baseline memory usage
      await this.establishBaseline();
      
      console.log('✅ PerformanceMonitor initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ PerformanceMonitor initialization failed:', error);
      return false;
    }
  }

  // Get comprehensive device information
  async getDeviceInfo() {
    try {
      const [
        totalMemory,
        availableMemory,
        usedMemory,
        deviceName,
        systemVersion,
        cpuType,
        batteryLevel,
        powerState
      ] = await Promise.all([
        DeviceInfo.getTotalMemory(),
        DeviceInfo.getAvailableLocationProviders(), // Fallback for available memory
        DeviceInfo.getUsedMemory(),
        DeviceInfo.getDeviceName(),
        DeviceInfo.getSystemVersion(),
        DeviceInfo.supportedAbis(),
        DeviceInfo.getBatteryLevel(),
        DeviceInfo.getPowerState()
      ]);

      return {
        platform: Platform.OS,
        deviceName,
        systemVersion,
        cpuType: cpuType[0] || 'unknown',
        totalMemory: Math.round(totalMemory / (1024 * 1024)), // MB
        usedMemory: Math.round(usedMemory / (1024 * 1024)), // MB
        availableMemory: Math.round((totalMemory - usedMemory) / (1024 * 1024)), // MB
        batteryLevel: Math.round(batteryLevel * 100),
        powerState
      };
    } catch (error) {
      console.error('Error getting device info:', error);
      return {
        platform: Platform.OS,
        error: error.message
      };
    }
  }

  // Establish baseline memory usage before model loading
  async establishBaseline() {
    try {
      const baseline = await this.getCurrentMetrics();
      this.baselineMemory = baseline;
      console.log('📊 Baseline established:', baseline);
      return baseline;
    } catch (error) {
      console.error('Error establishing baseline:', error);
      return null;
    }
  }

  // Get current system metrics
  async getCurrentMetrics() {
    try {
      const timestamp = Date.now();
      
      // Basic metrics from DeviceInfo
      const [usedMemory, totalMemory, batteryLevel, powerState] = await Promise.all([
        DeviceInfo.getUsedMemory(),
        DeviceInfo.getTotalMemory(),
        DeviceInfo.getBatteryLevel(),
        DeviceInfo.getPowerState()
      ]);

      let nativeMetrics = {};
      
      // Try to get native metrics if available
      if (this.nativeModule && this.nativeModule.getCurrentMetrics) {
        try {
          nativeMetrics = await this.nativeModule.getCurrentMetrics();
        } catch (error) {
          console.warn('Native metrics not available:', error.message);
        }
      }

      const metrics = {
        timestamp,
        memory: {
          used: Math.round(usedMemory / (1024 * 1024)), // MB
          total: Math.round(totalMemory / (1024 * 1024)), // MB
          available: Math.round((totalMemory - usedMemory) / (1024 * 1024)), // MB
          percentage: Math.round((usedMemory / totalMemory) * 100),
          // Native memory metrics (if available)
          ...nativeMetrics.memory
        },
        cpu: {
          // Will be populated by native module
          usage: nativeMetrics.cpu?.usage || 0,
          cores: nativeMetrics.cpu?.cores || 1,
          frequency: nativeMetrics.cpu?.frequency || 0
        },
        energy: {
          batteryLevel: Math.round(batteryLevel * 100),
          powerState: powerState.batteryState || 'unknown',
          isCharging: powerState.batteryState === 'charging',
          // Native energy metrics (if available)
          ...nativeMetrics.energy
        },
        process: {
          // Process-specific metrics from native module
          ...nativeMetrics.process
        }
      };

      return metrics;
    } catch (error) {
      console.error('Error getting current metrics:', error);
      return {
        timestamp: Date.now(),
        error: error.message
      };
    }
  }

  // Start continuous monitoring
  startMonitoring(intervalMs = 1000) {
    if (this.isMonitoring) {
      console.warn('⚠️ Performance monitoring already running');
      return;
    }

    console.log(`🔄 Starting performance monitoring (${intervalMs}ms interval)`);
    this.isMonitoring = true;
    
    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.getCurrentMetrics();
        this.performanceData.push(metrics);
        
        // Keep only last 100 data points to prevent memory bloat
        if (this.performanceData.length > 100) {
          this.performanceData = this.performanceData.slice(-100);
        }
        
        // Notify callbacks
        this.callbacks.forEach(callback => {
          try {
            callback(metrics);
          } catch (error) {
            console.error('Error in performance callback:', error);
          }
        });
        
      } catch (error) {
        console.error('Error during monitoring:', error);
      }
    }, intervalMs);
  }

  // Stop monitoring
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping performance monitoring');
    this.isMonitoring = false;
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }

  // Subscribe to performance updates
  subscribe(callback) {
    this.callbacks.add(callback);
    return () => this.callbacks.delete(callback);
  }

  // Get memory delta since baseline
  getMemoryDelta() {
    if (!this.baselineMemory || this.performanceData.length === 0) {
      return null;
    }

    const current = this.performanceData[this.performanceData.length - 1];
    return {
      used: current.memory.used - this.baselineMemory.memory.used,
      available: current.memory.available - this.baselineMemory.memory.available,
      percentage: current.memory.percentage - this.baselineMemory.memory.percentage
    };
  }

  // Get performance summary
  getSummary() {
    if (this.performanceData.length === 0) {
      return null;
    }

    const latest = this.performanceData[this.performanceData.length - 1];
    const memoryDelta = this.getMemoryDelta();
    
    return {
      current: latest,
      baseline: this.baselineMemory,
      delta: memoryDelta,
      isMonitoring: this.isMonitoring,
      dataPoints: this.performanceData.length,
      timespan: this.performanceData.length > 1 
        ? latest.timestamp - this.performanceData[0].timestamp 
        : 0
    };
  }

  // Export performance data
  exportData() {
    return {
      deviceInfo: this.deviceInfo,
      baseline: this.baselineMemory,
      performanceData: this.performanceData,
      summary: this.getSummary(),
      exportedAt: new Date().toISOString()
    };
  }

  // Export performance data to file
  async exportToFile() {
    try {
      const data = this.exportData();
      const filename = `performance_data_${Date.now()}.json`;
      const path = `${require('react-native-fs').DocumentDirectoryPath}/${filename}`;

      await require('react-native-fs').writeFile(path, JSON.stringify(data, null, 2), 'utf8');

      console.log(`📊 Performance data exported to: ${path}`);
      return { success: true, path, filename };
    } catch (error) {
      console.error('❌ Failed to export performance data:', error);
      return { success: false, error: error.message };
    }
  }

  // Log performance summary to console
  logSummary() {
    const summary = this.getSummary();
    if (!summary) {
      console.log('📊 No performance data available');
      return;
    }

    console.log('📊 Performance Summary:');
    console.log(`   Monitoring: ${summary.isMonitoring ? 'Active' : 'Inactive'}`);
    console.log(`   Data Points: ${summary.dataPoints}`);
    console.log(`   Timespan: ${Math.round(summary.timespan / 1000)}s`);

    if (summary.current) {
      console.log(`   Current Memory: ${summary.current.memory.used}MB / ${summary.current.memory.total}MB (${summary.current.memory.percentage}%)`);
      console.log(`   Current CPU: ${summary.current.cpu.usage.toFixed(1)}%`);
      console.log(`   Current Battery: ${summary.current.energy.batteryLevel}%`);
    }

    if (summary.delta) {
      console.log(`   Memory Delta: ${summary.delta.used > 0 ? '+' : ''}${summary.delta.used.toFixed(0)}MB`);
    }
  }

  // Clear performance data
  clearData() {
    this.performanceData = [];
    this.baselineMemory = null;
    console.log('🧹 Performance data cleared');
  }

  // Specific tracking for GGUF model inference
  async trackInference(inferenceFunction, inputTokens = 0) {
    return await this.trackOperation('GGUF Model Inference', async () => {
      const startTime = Date.now();
      const result = await inferenceFunction();
      const endTime = Date.now();

      // Add inference-specific metrics
      const inferenceMetrics = {
        inputTokens,
        outputTokens: result.tokens || 0,
        tokensPerSecond: result.tokens ? (result.tokens / ((endTime - startTime) / 1000)) : 0,
        timeToFirstToken: result.ttft || 0
      };

      return { ...result, inferenceMetrics };
    });
  }

  // Specific tracking for ONNX embedding operations
  async trackEmbedding(embeddingFunction, textLength = 0) {
    return await this.trackOperation('ONNX Embedding Generation', async () => {
      const startTime = Date.now();
      const result = await embeddingFunction();
      const endTime = Date.now();

      // Add embedding-specific metrics
      const embeddingMetrics = {
        textLength,
        embeddingDimension: result.length || 0,
        processingSpeed: textLength ? (textLength / ((endTime - startTime) / 1000)) : 0 // chars per second
      };

      return { embedding: result, embeddingMetrics };
    });
  }

  // Specific tracking for HNSW search operations
  async trackHNSWSearch(searchFunction, queryDimension = 0, k = 0) {
    return await this.trackOperation('HNSW Vector Search', async () => {
      const startTime = Date.now();
      const result = await searchFunction();
      const endTime = Date.now();

      // Add search-specific metrics
      const searchMetrics = {
        queryDimension,
        k,
        resultsFound: result.neighbors ? result.neighbors.length : 0,
        searchSpeed: k ? (k / ((endTime - startTime) / 1000)) : 0 // results per second
      };

      return { ...result, searchMetrics };
    });
  }

  // Enhanced operation tracking with native support
  async trackOperation(operationName, operation) {
    console.log(`📊 Tracking operation: ${operationName}`);

    const startTime = Date.now();
    let startMetrics, endMetrics;

    // Use native tracking if available for more accurate metrics
    if (this.nativeModule && this.nativeModule.startOperationTracking) {
      try {
        startMetrics = await this.nativeModule.startOperationTracking(operationName);
      } catch (error) {
        console.warn('Native operation tracking not available, falling back to JS tracking');
        startMetrics = await this.getCurrentMetrics();
      }
    } else {
      startMetrics = await this.getCurrentMetrics();
    }

    try {
      const result = await operation();

      const endTime = Date.now();

      // Use native tracking for end metrics
      if (this.nativeModule && this.nativeModule.endOperationTracking) {
        try {
          endMetrics = await this.nativeModule.endOperationTracking(operationName);
        } catch (error) {
          endMetrics = await this.getCurrentMetrics();
        }
      } else {
        endMetrics = await this.getCurrentMetrics();
      }

      const operationData = this._calculateOperationDelta(operationName, startTime, endTime, startMetrics, endMetrics, true);

      console.log(`✅ Operation completed: ${operationName}`, operationData);
      return { result, operationData };

    } catch (error) {
      const endTime = Date.now();
      endMetrics = await this.getCurrentMetrics();

      const operationData = this._calculateOperationDelta(operationName, startTime, endTime, startMetrics, endMetrics, false, error.message);

      console.error(`❌ Operation failed: ${operationName}`, operationData);
      throw error;
    }
  }

  // Calculate detailed operation deltas
  _calculateOperationDelta(operationName, startTime, endTime, startMetrics, endMetrics, success, errorMessage = null) {
    const duration = endTime - startTime;

    // Calculate memory deltas
    const memoryDelta = {
      used: endMetrics.memory.used - startMetrics.memory.used,
      available: endMetrics.memory.available - startMetrics.memory.available,
      percentage: endMetrics.memory.percentage - startMetrics.memory.percentage,
      // Native heap deltas (if available)
      nativeHeapAllocated: endMetrics.memory.nativeHeapAllocatedMB ?
        endMetrics.memory.nativeHeapAllocatedMB - (startMetrics.memory.nativeHeapAllocatedMB || 0) : 0,
      // Process memory deltas
      processPss: endMetrics.memory.processPssMB ?
        endMetrics.memory.processPssMB - (startMetrics.memory.processPssMB || 0) : 0
    };

    // Calculate CPU deltas
    const cpuDelta = {
      usage: endMetrics.cpu.usage - (startMetrics.cpu.usage || 0),
      systemUsage: endMetrics.cpu.systemUsage ?
        endMetrics.cpu.systemUsage - (startMetrics.cpu.systemUsage || 0) : 0,
      processUsage: endMetrics.cpu.processUsage ?
        endMetrics.cpu.processUsage - (startMetrics.cpu.processUsage || 0) : 0
    };

    return {
      name: operationName,
      startTime,
      endTime,
      duration,
      startMetrics,
      endMetrics,
      memoryDelta,
      cpuDelta,
      success,
      error: errorMessage,
      // Performance indicators
      memoryEfficiency: memoryDelta.used > 0 ? duration / memoryDelta.used : 0,
      cpuEfficiency: cpuDelta.usage > 0 ? duration / cpuDelta.usage : 0
    };
  }
}

// Export singleton instance
const performanceMonitor = new PerformanceMonitor();
export default performanceMonitor;
