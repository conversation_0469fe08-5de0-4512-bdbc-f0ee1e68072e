// src/services/RAGService.js - Enhanced RAG service with dynamic index building
import RNFS from 'react-native-fs';
import { Platform } from 'react-native';
import { NativeModules } from 'react-native';
import * as ort from 'onnxruntime-react-native';
import LocalTokenizer from '../utils/LocalTokenizer';
import BundledTokenizer from '../utils/BundledTokenizer';

const { HnswSearchModule } = NativeModules;

class RAGService {
  constructor() {
    // Base RAG properties
    this.embeddingSession = null;
    this.tokenizer = null;
    this.metadataIndex = null;
    this.metadataCache = new Map();
    this.cacheStats = { hits: 0, misses: 0 };
    this.isInitialized = false;
    this.isLoading = false;

    // Enhanced properties for user documents
    this.userIndexPath = `${RNFS.DocumentDirectoryPath}/user_documents_hnsw_rs`;
    this.userMetadataPath = `${RNFS.DocumentDirectoryPath}/user_metadata.json`;
    this.userDocuments = new Map();
    this.nextDocumentId = 0;
    this.isUserIndexInitialized = false;
  }

  // Initialize the RAG system
  async initialize() {
    if (this.isInitialized || this.isLoading) {
      return this.isInitialized;
    }

    this.isLoading = true;
    console.log("🚀 Initializing RAG Service...");

    try {
      await this._copyAssetsIfNeeded();
      await this._initializeEmbeddingModel();
      await this._initializeTokenizer();
      await this._loadMetadataIndex();

      // Load existing user documents if any
      await this.loadUserDocuments();

      // Get the next available document ID
      await this.initializeDocumentId();

      this.isInitialized = true;
      console.log("✅ RAG Service initialized successfully");

      // Preload popular chunks in background
      setTimeout(() => this._preloadPopularChunks(), 2000);

      return true;
    } catch (error) {
      console.error("❌ RAG Service initialization failed:", error);
      this.isInitialized = false;
      throw error;
    } finally {
      this.isLoading = false;
    }
  }

  // Load existing user documents from metadata file
  async loadUserDocuments() {
    try {
      const exists = await RNFS.exists(this.userMetadataPath);
      if (exists) {
        const metadataContent = await RNFS.readFile(this.userMetadataPath, 'utf8');
        const metadata = JSON.parse(metadataContent);

        // Restore user documents map
        this.userDocuments.clear();
        metadata.documents.forEach(doc => {
          this.userDocuments.set(doc.id, doc);
        });

        this.isUserIndexInitialized = metadata.indexInitialized || false;
        console.log(`📄 Loaded ${this.userDocuments.size} user documents`);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load user documents:', error);
      this.userDocuments.clear();
      this.isUserIndexInitialized = false;
    }
  }

  // Save user documents metadata to file
  async saveUserDocuments() {
    try {
      const metadata = {
        documents: Array.from(this.userDocuments.values()),
        indexInitialized: this.isUserIndexInitialized,
        lastUpdated: new Date().toISOString()
      };

      await RNFS.writeFile(this.userMetadataPath, JSON.stringify(metadata, null, 2), 'utf8');
      console.log(`💾 Saved ${this.userDocuments.size} user documents to metadata`);
    } catch (error) {
      console.error('❌ Failed to save user documents:', error);
      throw error;
    }
  }

  // Initialize the next document ID based on existing indices
  async initializeDocumentId() {
    try {
      // Get count from main index
      const mainCount = this.metadataIndex?.total_documents || 0;

      // Get count from user index if it exists
      let userCount = 0;
      if (this.isUserIndexInitialized) {
        try {
          const result = await HnswSearchModule.getDocumentCount(
            RNFS.DocumentDirectoryPath,
            'user_documents_hnsw_rs'
          );
          userCount = result.count || 0;
        } catch (error) {
          console.warn('⚠️ Failed to get user index count:', error);
        }
      }

      // Set next ID to be after both indices
      this.nextDocumentId = Math.max(mainCount, userCount) + 1000; // Add buffer
      console.log(`🔢 Next document ID set to: ${this.nextDocumentId}`);
    } catch (error) {
      console.warn('⚠️ Failed to initialize document ID:', error);
      this.nextDocumentId = 100000; // Fallback to high number
    }
  }

  // Add a new document with native index building
  async addDocument(documentData) {
    try {
      console.log(`📄 Adding document with native indexing: ${documentData.title || 'Untitled'}`);

      // Generate embedding using the base service
      const embedding = await this.embedText(documentData.content);

      // Create document metadata
      const docId = this.nextDocumentId++;
      const docMetadata = {
        id: docId,
        title: documentData.title || 'User Document',
        content: documentData.content,
        source: documentData.source || 'user_upload',
        type: documentData.type || 'text',
        timestamp: new Date().toISOString(),
        embedding: Array.from(embedding)
      };

      // Store in user documents map
      this.userDocuments.set(docId, docMetadata);

      // Build or update the native HNSW index
      await this.updateNativeIndex([docMetadata]);

      // Save metadata to disk
      await this.saveUserDocuments();

      console.log(`✅ Document added with native indexing: ${docId}`);

      return {
        success: true,
        documentId: docId,
        message: "Document added to native RAG index"
      };

    } catch (error) {
      console.error(`❌ Failed to add document with native indexing:`, error);
      throw error;
    }
  }

  // Update the native HNSW index with new documents
  async updateNativeIndex(newDocuments) {
    try {
      if (!newDocuments || newDocuments.length === 0) {
        return;
      }

      const embeddings = [];
      newDocuments.forEach(doc => {
        embeddings.push(...doc.embedding);
      });

      const dimension = newDocuments[0].embedding.length;

      if (!this.isUserIndexInitialized) {
        // Build new index
        console.log(`🔨 Building new user index with ${newDocuments.length} documents`);

        await HnswSearchModule.buildIndex(
          embeddings,
          newDocuments.length,
          dimension,
          RNFS.DocumentDirectoryPath,
          'user_documents_hnsw_rs',
          16, // m parameter
          200 // ef_construction parameter
        );

        this.isUserIndexInitialized = true;
      } else {
        // Add to existing index
        console.log(`➕ Adding ${newDocuments.length} documents to existing user index`);

        const startId = newDocuments[0].id;

        await HnswSearchModule.addToIndex(
          embeddings,
          newDocuments.length,
          dimension,
          RNFS.DocumentDirectoryPath,
          'user_documents_hnsw_rs',
          startId
        );
      }

      console.log(`✅ Native index updated successfully`);
    } catch (error) {
      console.error('❌ Failed to update native index:', error);
      throw error;
    }
  }

  // Remove user document
  async removeUserDocument(documentId) {
    if (!this.userDocuments.has(documentId)) {
      throw new Error(`Document ${documentId} not found`);
    }

    this.userDocuments.delete(documentId);

    // Save updated metadata
    await this.saveUserDocuments();

    console.log(`🗑️ Removed user document: ${documentId}`);

    return {
      success: true,
      message: "Document removed successfully"
    };
  }

  // Get user documents list
  getUserDocuments() {
    return Array.from(this.userDocuments.values()).map(doc => ({
      id: doc.id,
      title: doc.title,
      source: doc.source,
      type: doc.type,
      timestamp: doc.timestamp,
      contentLength: doc.content.length,
      contentPreview: doc.content.substring(0, 100) + (doc.content.length > 100 ? '...' : '')
    }));
  }

  // Get enhanced statistics
  getStats() {
    const userDocsCount = this.userDocuments ? this.userDocuments.size : 0;
    const userDocsSize = this.userDocuments ?
      Array.from(this.userDocuments.values()).reduce((sum, doc) => sum + doc.content.length, 0) : 0;

    return {
      isInitialized: this.isInitialized,
      totalDocuments: this.metadataIndex?.total_documents || 0,
      totalChunks: this.metadataIndex?.total_files || 0,
      cachedChunks: this.metadataCache.size,
      cacheHitRate: this.cacheStats.hits + this.cacheStats.misses > 0
        ? Math.round(this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses) * 100)
        : 0,
      memoryEstimate: `${Math.round(this.metadataCache.size * 5)}MB`,
      userDocuments: userDocsCount,
      userDocumentsSize: `${Math.round(userDocsSize / 1024)}KB`,
      userIndexInitialized: this.isUserIndexInitialized,
      nextDocumentId: this.nextDocumentId,
      totalDocumentsIncludingUser: (this.metadataIndex?.total_documents || 0) + userDocsCount
    };
  }

  // Clear all caches
  clearCaches() {
    this.metadataCache.clear();
    this.cacheStats = { hits: 0, misses: 0 };
    console.log("🧹 RAG caches cleared");

    // Clear native cache
    if (HnswSearchModule.clearCache) {
      HnswSearchModule.clearCache().catch(error => {
        console.warn('⚠️ Failed to clear native cache:', error);
      });
    }
  }

  // Placeholder methods that need to be implemented with the original RAG functionality
  async _copyAssetsIfNeeded() {
    // TODO: Implement asset copying logic
    console.log("📦 Asset copying not yet implemented");
  }

  async _initializeEmbeddingModel() {
    // TODO: Implement embedding model initialization
    console.log("🧠 Embedding model initialization not yet implemented");
  }

  async _initializeTokenizer() {
    // TODO: Implement tokenizer initialization
    console.log("🔤 Tokenizer initialization not yet implemented");
  }

  async _loadMetadataIndex() {
    // TODO: Implement metadata index loading
    console.log("📂 Metadata index loading not yet implemented");
  }

  async _preloadPopularChunks() {
    // TODO: Implement chunk preloading
    console.log("🚀 Chunk preloading not yet implemented");
  }

  async embedText(text) {
    // TODO: Implement text embedding
    console.log("🧮 Text embedding not yet implemented");
    return new Array(384).fill(0); // Placeholder embedding
  }

  async querySimilarDocuments(query, k = 5) {
    // TODO: Implement document querying
    console.log("🔍 Document querying not yet implemented");
    return { documents: [], queryTime: 0, cacheStats: this.cacheStats };
  }

  async testTermEmbedding(term) {
    // TODO: Implement term testing
    console.log("🧪 Term testing not yet implemented");
    return { error: "Not implemented" };
  }

  async runDiagnostics() {
    // TODO: Implement diagnostics
    console.log("🔧 Diagnostics not yet implemented");
    return { results: {} };
  }
}

// Export singleton instance
export default new RAGService();