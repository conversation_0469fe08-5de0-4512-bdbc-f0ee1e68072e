// src/config/ModelConfig.js

// Model context window configurations
export const MODEL_CONFIGS = {
  "Llama-3.2-1B-Instruct": { 
    contextWindow: 130000, 
    ragChunks: 15,
    description: "Large context, excellent for RAG"
  },
  "DeepSeek-R1-Distill-Qwen-1.5B": { 
    contextWindow: 131072, 
    ragChunks: 15,
    description: "Large context, reasoning focused"
  },  
  "Qwen2-0.5B-Instruct": { 
    contextWindow: 32768, 
    ragChunks: 10,
    description: "Medium context, efficient"
  },
  "SmolLM2-1.7B-Instruct": { 
    contextWindow: 8192, 
    ragChunks: 6,
    description: "Small context, fast inference"
  },
  "Phi-2-2.8B": { 
    contextWindow: 2048, 
    ragChunks: 3,
    description: "Very small context, basic RAG"
  },
  "Phi-3.5-mini-instruct-3.8B": { 
    contextWindow: 128000, 
    ragChunks: 15,
    description: "Large context, balanced performance"
  },
  "Phi-4-mini-instruct-3.8B": { 
    contextWindow: 128000, 
    ragChunks: 20,
    description: "Large context, latest model"
  },
};

// HuggingFace to GGUF mapping
export const HF_TO_GGUF = {
  "Llama-3.2-1B-Instruct": "medmekk/Llama-3.2-1B-Instruct.GGUF",
  "DeepSeek-R1-Distill-Qwen-1.5B": "medmekk/DeepSeek-R1-Distill-Qwen-1.5B.GGUF",
  "Qwen2-0.5B-Instruct": "medmekk/Qwen2.5-0.5B-Instruct.GGUF",
  "SmolLM2-1.7B-Instruct": "medmekk/SmolLM2-1.7B-Instruct.GGUF",
  "Phi-2-2.8B": "TheBloke/phi-2-GGUF",
  "Phi-3.5-mini-instruct-3.8B": "bartowski/Phi-3.5-mini-instruct-GGUF",
  "Phi-4-mini-instruct-3.8B": "bartowski/microsoft_Phi-4-mini-instruct-GGUF",
};

// Model formats for selection
export const MODEL_FORMATS = [
  { 
    label: "Llama-3.2-1B-Instruct",
    recommended: true,
    description: "Best overall balance"
  },
  { 
    label: "Qwen2-0.5B-Instruct",
    recommended: false,
    description: "Fast and efficient"
  },
  { 
    label: "DeepSeek-R1-Distill-Qwen-1.5B",
    recommended: false,
    description: "Enhanced reasoning"
  },
  { 
    label: "SmolLM2-1.7B-Instruct",
    recommended: false,
    description: "Good for low-memory devices"
  },
  { 
    label: "Phi-2-2.8B",
    recommended: false,
    description: "Basic functionality"
  },
  { 
    label: "Phi-3.5-mini-instruct-3.8B",
    recommended: true,
    description: "Latest generation"
  },
  { 
    label: "Phi-4-mini-instruct-3.8B",
    recommended: true,
    description: "Most advanced"
  },
];

// Function to get optimal chunk count for current model
export const getOptimalChunkCount = (modelName) => {
  if (!modelName) return 5; // Default fallback
  
  // Extract base model name from GGUF filename
  const baseModelName = Object.keys(MODEL_CONFIGS).find(name => 
    modelName.includes(name.split('-')[0]) || 
    modelName.toLowerCase().includes(name.toLowerCase().split('-')[0])
  );
  
  if (baseModelName) {
    return MODEL_CONFIGS[baseModelName].ragChunks;
  }
  
  // Fallback based on filename patterns
  if (modelName.toLowerCase().includes('phi-4')) return 20;
  if (modelName.toLowerCase().includes('phi-3')) return 15;
  if (modelName.toLowerCase().includes('llama')) return 15;
  if (modelName.toLowerCase().includes('qwen')) return 10;
  if (modelName.toLowerCase().includes('smol')) return 6;
  if (modelName.toLowerCase().includes('phi-2')) return 3;
  
  return 5; // Safe default
};

// Get model info by name
export const getModelInfo = (modelName) => {
  const baseModelName = Object.keys(MODEL_CONFIGS).find(name => 
    modelName?.includes(name.split('-')[0])
  );
  
  if (baseModelName) {
    return {
      ...MODEL_CONFIGS[baseModelName],
      name: baseModelName,
      ggufRepo: HF_TO_GGUF[baseModelName]
    };
  }
  
  return {
    contextWindow: 8192,
    ragChunks: 5,
    description: "Unknown model",
    name: modelName || "Unknown",
    ggufRepo: null
  };
};

// Validate model compatibility
export const validateModelForRAG = (modelName, documentCount = 0) => {
  const modelInfo = getModelInfo(modelName);
  const tokensPerDoc = 250; // Approximate tokens per 1000-char chunk
  const ragTokens = modelInfo.ragChunks * tokensPerDoc;
  const systemTokens = 100;
  const userTokens = 50;
  const responseTokens = 500;
  const historyTokens = 200;
  
  const totalUsed = ragTokens + systemTokens + userTokens + responseTokens + historyTokens;
  const available = modelInfo.contextWindow;
  const utilization = (totalUsed / available) * 100;
  
  return {
    compatible: utilization < 90,
    utilization: Math.round(utilization),
    ragTokens,
    availableTokens: available - totalUsed,
    recommendation: utilization > 90 ? 
      `Reduce RAG chunks to ${Math.floor(modelInfo.ragChunks * 0.7)}` : 
      'Optimal configuration'
  };
};

export default {
  MODEL_CONFIGS,
  HF_TO_GGUF,
  MODEL_FORMATS,
  getOptimalChunkCount,
  getModelInfo,
  validateModelForRAG
};