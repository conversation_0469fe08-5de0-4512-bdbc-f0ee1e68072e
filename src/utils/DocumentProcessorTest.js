// src/utils/DocumentProcessorTest.js - Simple test for document processing
import DocumentProcessor from './DocumentProcessor';

// Test the document processor functionality
export const testDocumentProcessor = () => {
  console.log('🧪 Testing Document Processor...');

  // Test supported types
  console.log('Supported types:', DocumentProcessor.supportedTypes);
  console.log('Is text/plain supported:', DocumentProcessor.isSupportedType('text/plain'));
  console.log('Is application/pdf supported:', DocumentProcessor.isSupportedType('application/pdf'));
  console.log('Is image/jpeg supported:', DocumentProcessor.isSupportedType('image/jpeg'));

  // Test JSON to text conversion
  const testJson = {
    name: "John Doe",
    age: 30,
    address: {
      street: "123 Main St",
      city: "Anytown",
      country: "USA"
    },
    hobbies: ["reading", "swimming", "coding"]
  };

  const jsonText = DocumentProcessor.jsonToText(testJson);
  console.log('JSON to text conversion:');
  console.log(jsonText);

  // Test CSV parsing
  const testCsv = 'name,age,city\nJohn,30,New York\nJane,25,Los Angeles\nBob,35,Chicago';
  const csvText = DocumentProcessor.csvToText(testCsv);
  console.log('CSV to text conversion:');
  console.log(csvText);

  // Test content chunking
  const longText = 'This is a very long text that needs to be chunked. '.repeat(50);
  const chunks = DocumentProcessor.chunkContent(longText, 200, 50);
  console.log(`Content chunking: ${longText.length} chars -> ${chunks.length} chunks`);
  chunks.forEach((chunk, index) => {
    console.log(`Chunk ${index + 1}: ${chunk.length} chars`);
  });

  // Test file size validation
  try {
    DocumentProcessor.validateFileSize(5 * 1024 * 1024); // 5MB
    console.log('✅ File size validation passed for 5MB');
  } catch (error) {
    console.log('❌ File size validation failed:', error.message);
  }

  try {
    DocumentProcessor.validateFileSize(15 * 1024 * 1024); // 15MB
    console.log('✅ File size validation passed for 15MB');
  } catch (error) {
    console.log('❌ File size validation failed:', error.message);
  }

  console.log('✅ Document Processor tests completed');
};

export default testDocumentProcessor;
