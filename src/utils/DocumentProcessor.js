// src/utils/DocumentProcessor.js - Document processing utilities for user uploads
import RNFS from 'react-native-fs';

class DocumentProcessor {
  constructor() {
    this.supportedTypes = [
      'text/plain',
      'application/json',
      'text/csv',
      'application/pdf' // Note: PDF processing would require additional libraries
    ];
  }

  // Check if file type is supported
  isSupportedType(mimeType) {
    return this.supportedTypes.includes(mimeType);
  }

  // Process document based on type
  async processDocument(documentUri, mimeType, fileName) {
    try {
      console.log(`📄 Processing document: ${fileName} (${mimeType})`);

      if (!this.isSupportedType(mimeType)) {
        throw new Error(`Unsupported file type: ${mimeType}`);
      }

      let content = '';
      let title = fileName || 'Untitled Document';

      switch (mimeType) {
        case 'text/plain':
          content = await this.processTextFile(documentUri);
          break;
        case 'application/json':
          content = await this.processJsonFile(documentUri);
          break;
        case 'text/csv':
          content = await this.processCsvFile(documentUri);
          break;
        case 'application/pdf':
          // PDF processing would require additional libraries like react-native-pdf
          throw new Error('PDF processing not yet implemented');
        default:
          throw new Error(`Unsupported file type: ${mimeType}`);
      }

      // Basic content validation
      if (!content || content.trim().length === 0) {
        throw new Error('Document appears to be empty');
      }

      // Chunk content if it's too large (optional)
      const chunks = this.chunkContent(content);

      return {
        title,
        content,
        chunks,
        type: mimeType,
        source: fileName,
        processedAt: new Date().toISOString(),
        wordCount: content.split(/\s+/).length,
        characterCount: content.length
      };

    } catch (error) {
      console.error(`❌ Failed to process document ${fileName}:`, error);
      throw error;
    }
  }

  // Process plain text file
  async processTextFile(uri) {
    try {
      const content = await RNFS.readFile(uri, 'utf8');
      return content;
    } catch (error) {
      throw new Error(`Failed to read text file: ${error.message}`);
    }
  }

  // Process JSON file
  async processJsonFile(uri) {
    try {
      const jsonContent = await RNFS.readFile(uri, 'utf8');
      const parsed = JSON.parse(jsonContent);
      
      // Convert JSON to readable text format
      return this.jsonToText(parsed);
    } catch (error) {
      throw new Error(`Failed to process JSON file: ${error.message}`);
    }
  }

  // Process CSV file
  async processCsvFile(uri) {
    try {
      const csvContent = await RNFS.readFile(uri, 'utf8');
      return this.csvToText(csvContent);
    } catch (error) {
      throw new Error(`Failed to process CSV file: ${error.message}`);
    }
  }

  // Convert JSON to readable text
  jsonToText(jsonObj, prefix = '') {
    let text = '';
    
    if (Array.isArray(jsonObj)) {
      jsonObj.forEach((item, index) => {
        text += `${prefix}Item ${index + 1}:\n`;
        text += this.jsonToText(item, prefix + '  ') + '\n';
      });
    } else if (typeof jsonObj === 'object' && jsonObj !== null) {
      Object.entries(jsonObj).forEach(([key, value]) => {
        if (typeof value === 'object') {
          text += `${prefix}${key}:\n`;
          text += this.jsonToText(value, prefix + '  ') + '\n';
        } else {
          text += `${prefix}${key}: ${value}\n`;
        }
      });
    } else {
      text += `${prefix}${jsonObj}\n`;
    }
    
    return text;
  }

  // Convert CSV to readable text
  csvToText(csvContent) {
    const lines = csvContent.split('\n').filter(line => line.trim());
    if (lines.length === 0) return '';

    let text = '';
    const headers = this.parseCsvLine(lines[0]);
    
    text += `CSV Data with ${headers.length} columns:\n`;
    text += `Headers: ${headers.join(', ')}\n\n`;

    // Process data rows
    for (let i = 1; i < Math.min(lines.length, 101); i++) { // Limit to first 100 rows
      const values = this.parseCsvLine(lines[i]);
      text += `Row ${i}:\n`;
      
      headers.forEach((header, index) => {
        const value = values[index] || '';
        text += `  ${header}: ${value}\n`;
      });
      text += '\n';
    }

    if (lines.length > 101) {
      text += `... and ${lines.length - 101} more rows\n`;
    }

    return text;
  }

  // Simple CSV line parser (handles basic cases)
  parseCsvLine(line) {
    const result = [];
    let current = '';
    let inQuotes = false;

    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    result.push(current.trim());
    return result;
  }

  // Chunk content into smaller pieces for better RAG performance
  chunkContent(content, maxChunkSize = 1000, overlap = 100) {
    if (content.length <= maxChunkSize) {
      return [content];
    }

    const chunks = [];
    let start = 0;

    while (start < content.length) {
      let end = Math.min(start + maxChunkSize, content.length);
      
      // Try to break at sentence boundaries
      if (end < content.length) {
        const lastSentence = content.lastIndexOf('.', end);
        const lastNewline = content.lastIndexOf('\n', end);
        const breakPoint = Math.max(lastSentence, lastNewline);
        
        if (breakPoint > start + maxChunkSize * 0.5) {
          end = breakPoint + 1;
        }
      }

      chunks.push(content.substring(start, end).trim());
      start = Math.max(start + maxChunkSize - overlap, end);
    }

    return chunks.filter(chunk => chunk.length > 0);
  }

  // Get supported file extensions
  getSupportedExtensions() {
    return {
      'text/plain': ['.txt', '.md', '.log'],
      'application/json': ['.json'],
      'text/csv': ['.csv'],
      'application/pdf': ['.pdf']
    };
  }

  // Validate file size (limit to reasonable sizes for mobile processing)
  validateFileSize(fileSize, maxSizeMB = 10) {
    const maxSizeBytes = maxSizeMB * 1024 * 1024;
    if (fileSize > maxSizeBytes) {
      throw new Error(`File size (${Math.round(fileSize / 1024 / 1024)}MB) exceeds limit of ${maxSizeMB}MB`);
    }
    return true;
  }
}

export default new DocumentProcessor();
