// src/components/DocumentManager.js - Document management interface
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  TextInput,
  ActivityIndicator,
  Modal
} from 'react-native';
import DocumentPicker from '@react-native-documents/picker';
import Icon from 'react-native-vector-icons/FontAwesome';
import RAGService from '../services/RAGService';
import DocumentProcessor from '../utils/DocumentProcessor';

const DocumentManager = ({ visible, onClose, onDocumentAdded }) => {
  const [userDocuments, setUserDocuments] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showTextInput, setShowTextInput] = useState(false);
  const [textTitle, setTextTitle] = useState('');
  const [textContent, setTextContent] = useState('');

  useEffect(() => {
    if (visible) {
      loadUserDocuments();
    }
  }, [visible]);

  const loadUserDocuments = () => {
    try {
      const docs = RAGService.getUserDocuments();
      setUserDocuments(docs);
    } catch (error) {
      console.error('Failed to load user documents:', error);
    }
  };

  const handlePickDocument = async () => {
    try {
      setIsLoading(true);
      
      const result = await DocumentPicker.pick({
        type: [
          DocumentPicker.types.plainText,
          DocumentPicker.types.json,
          DocumentPicker.types.csv,
          // DocumentPicker.types.pdf, // Uncomment when PDF support is added
        ],
        copyTo: 'documentDirectory'
      });

      if (result && result.length > 0) {
        const file = result[0];
        
        // Validate file size
        try {
          DocumentProcessor.validateFileSize(file.size);
        } catch (error) {
          Alert.alert('File Too Large', error.message);
          return;
        }

        // Process the document
        const processedDoc = await DocumentProcessor.processDocument(
          file.fileCopyUri || file.uri,
          file.type,
          file.name
        );

        // Add to RAG system
        const addResult = await RAGService.addDocument(processedDoc);
        
        if (addResult.success) {
          Alert.alert(
            'Document Added',
            `Successfully added "${processedDoc.title}" to your knowledge base.\n\nWords: ${processedDoc.wordCount}\nCharacters: ${processedDoc.characterCount}`,
            [{ text: 'OK', onPress: () => {
              loadUserDocuments();
              onDocumentAdded && onDocumentAdded();
            }}]
          );
        }
      }
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        console.log('Document picker cancelled');
      } else {
        console.error('Document picker error:', error);
        Alert.alert('Error', `Failed to process document: ${error.message}`);
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddTextDocument = async () => {
    if (!textTitle.trim() || !textContent.trim()) {
      Alert.alert('Missing Information', 'Please provide both title and content.');
      return;
    }

    try {
      setIsLoading(true);

      const documentData = {
        title: textTitle.trim(),
        content: textContent.trim(),
        type: 'text/plain',
        source: 'manual_input'
      };

      const result = await RAGService.addDocument(documentData);
      
      if (result.success) {
        Alert.alert(
          'Document Added',
          `Successfully added "${documentData.title}" to your knowledge base.`,
          [{ text: 'OK', onPress: () => {
            setTextTitle('');
            setTextContent('');
            setShowTextInput(false);
            loadUserDocuments();
            onDocumentAdded && onDocumentAdded();
          }}]
        );
      }
    } catch (error) {
      console.error('Failed to add text document:', error);
      Alert.alert('Error', `Failed to add document: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveDocument = (docId, docTitle) => {
    Alert.alert(
      'Remove Document',
      `Are you sure you want to remove "${docTitle}" from your knowledge base?`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Remove', 
          style: 'destructive',
          onPress: async () => {
            try {
              await RAGService.removeUserDocument(docId);
              loadUserDocuments();
              Alert.alert('Success', 'Document removed successfully.');
            } catch (error) {
              Alert.alert('Error', `Failed to remove document: ${error.message}`);
            }
          }
        }
      ]
    );
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleDateString() + ' ' + 
           new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <Text style={styles.title}>Document Manager</Text>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icon name="times" size={24} color="#666" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content}>
          {/* Add Document Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Add Documents</Text>
            
            <TouchableOpacity 
              style={styles.actionButton} 
              onPress={handlePickDocument}
              disabled={isLoading}
            >
              <Icon name="file-o" size={20} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Pick File</Text>
            </TouchableOpacity>

            <TouchableOpacity 
              style={[styles.actionButton, styles.secondaryButton]} 
              onPress={() => setShowTextInput(!showTextInput)}
              disabled={isLoading}
            >
              <Icon name="edit" size={20} color="#3B82F6" />
              <Text style={[styles.actionButtonText, styles.secondaryButtonText]}>Add Text</Text>
            </TouchableOpacity>

            {/* Text Input Section */}
            {showTextInput && (
              <View style={styles.textInputSection}>
                <TextInput
                  style={styles.titleInput}
                  placeholder="Document title..."
                  value={textTitle}
                  onChangeText={setTextTitle}
                />
                <TextInput
                  style={styles.contentInput}
                  placeholder="Document content..."
                  value={textContent}
                  onChangeText={setTextContent}
                  multiline
                  numberOfLines={8}
                />
                <View style={styles.textInputButtons}>
                  <TouchableOpacity 
                    style={styles.cancelButton}
                    onPress={() => {
                      setShowTextInput(false);
                      setTextTitle('');
                      setTextContent('');
                    }}
                  >
                    <Text style={styles.cancelButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={styles.addButton}
                    onPress={handleAddTextDocument}
                    disabled={isLoading}
                  >
                    <Text style={styles.addButtonText}>Add Document</Text>
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>

          {/* Documents List */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>
              Your Documents ({userDocuments.length})
            </Text>
            
            {userDocuments.length === 0 ? (
              <View style={styles.emptyState}>
                <Icon name="folder-open-o" size={48} color="#CBD5E1" />
                <Text style={styles.emptyStateText}>No documents added yet</Text>
                <Text style={styles.emptyStateSubtext}>
                  Add documents to enhance your AI's knowledge base
                </Text>
              </View>
            ) : (
              userDocuments.map((doc) => (
                <View key={doc.id} style={styles.documentItem}>
                  <View style={styles.documentInfo}>
                    <Text style={styles.documentTitle}>{doc.title}</Text>
                    <Text style={styles.documentMeta}>
                      {doc.type} • {doc.contentLength} chars • {formatTimestamp(doc.timestamp)}
                    </Text>
                    <Text style={styles.documentPreview}>{doc.contentPreview}</Text>
                  </View>
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => handleRemoveDocument(doc.id, doc.title)}
                  >
                    <Icon name="trash-o" size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              ))
            )}
          </View>

          {/* Supported Formats */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Supported Formats</Text>
            <Text style={styles.supportedFormats}>
              • Plain text (.txt, .md)
              {'\n'}• JSON files (.json)
              {'\n'}• CSV files (.csv)
              {'\n'}• Manual text input
            </Text>
          </View>
        </ScrollView>

        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text style={styles.loadingText}>Processing document...</Text>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E2E8F0',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1E293B',
  },
  closeButton: {
    padding: 8,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  actionButton: {
    backgroundColor: '#3B82F6',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryButtonText: {
    color: '#3B82F6',
  },
  textInputSection: {
    marginTop: 16,
    padding: 16,
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
  },
  titleInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    padding: 12,
    fontSize: 16,
    marginBottom: 12,
  },
  contentInput: {
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E2E8F0',
    borderRadius: 6,
    padding: 12,
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: 'top',
    marginBottom: 12,
  },
  textInputButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 8,
  },
  cancelButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  cancelButtonText: {
    color: '#6B7280',
    fontSize: 14,
    fontWeight: '500',
  },
  addButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  documentItem: {
    flexDirection: 'row',
    backgroundColor: '#F8FAFC',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  documentInfo: {
    flex: 1,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1E293B',
    marginBottom: 4,
  },
  documentMeta: {
    fontSize: 12,
    color: '#6B7280',
    marginBottom: 4,
  },
  documentPreview: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 18,
  },
  removeButton: {
    padding: 8,
    alignSelf: 'flex-start',
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
  },
  emptyStateText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#6B7280',
    marginTop: 12,
  },
  emptyStateSubtext: {
    fontSize: 14,
    color: '#9CA3AF',
    textAlign: 'center',
    marginTop: 4,
  },
  supportedFormats: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#6B7280',
  },
});

export default DocumentManager;
