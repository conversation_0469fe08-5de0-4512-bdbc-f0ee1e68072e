// src/components/PerformanceComponents.js - Performance monitoring UI components
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Platform
} from 'react-native';
import Icon from 'react-native-vector-icons/FontAwesome';
import PerformanceMonitor from '../services/PerformanceMonitor';

// Real-time performance metrics display
export const PerformanceMetricsDisplay = ({ style }) => {
  const [metrics, setMetrics] = useState(null);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    const unsubscribe = PerformanceMonitor.subscribe((newMetrics) => {
      setMetrics(newMetrics);
    });

    // Get initial metrics
    PerformanceMonitor.getCurrentMetrics().then(setMetrics);

    return unsubscribe;
  }, []);

  const toggleMonitoring = () => {
    if (isMonitoring) {
      PerformanceMonitor.stopMonitoring();
      setIsMonitoring(false);
    } else {
      PerformanceMonitor.startMonitoring(1000);
      setIsMonitoring(true);
    }
  };

  const exportData = async () => {
    try {
      const result = await PerformanceMonitor.exportToFile();
      if (result.success) {
        Alert.alert(
          'Export Successful',
          `Performance data exported to:\n${result.filename}`,
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert('Export Failed', result.error);
      }
    } catch (error) {
      Alert.alert('Export Failed', error.message);
    }
  };

  const clearData = () => {
    Alert.alert(
      'Clear Performance Data',
      'Are you sure you want to clear all performance data?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: () => {
            PerformanceMonitor.clearData();
            setMetrics(null);
          }
        }
      ]
    );
  };

  const formatMemory = (mb) => {
    if (mb >= 1024) {
      return `${(mb / 1024).toFixed(1)}GB`;
    }
    return `${mb.toFixed(0)}MB`;
  };

  if (!metrics) {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.loadingText}>Loading performance metrics...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      {/* Header */}
      <TouchableOpacity 
        style={styles.header} 
        onPress={() => setIsExpanded(!isExpanded)}
      >
        <View style={styles.headerLeft}>
          <Icon name="tachometer" size={16} color="#3B82F6" />
          <Text style={styles.headerTitle}>Performance Monitor</Text>
          {isMonitoring && <View style={styles.monitoringIndicator} />}
        </View>
        <Icon 
          name={isExpanded ? "chevron-up" : "chevron-down"} 
          size={12} 
          color="#64748B" 
        />
      </TouchableOpacity>

      {/* Quick Stats */}
      <View style={styles.quickStats}>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Memory</Text>
          <Text style={styles.statValue}>
            {formatMemory(metrics.memory.used)} / {formatMemory(metrics.memory.total)}
          </Text>
          <Text style={styles.statPercentage}>{metrics.memory.percentage}%</Text>
          {metrics.memory.memoryPressure && (
            <Text style={[styles.statDetail, { color: metrics.memory.memoryPressure > 80 ? '#ff6b6b' : '#4ecdc4' }]}>
              Pressure: {metrics.memory.memoryPressure.toFixed(0)}%
            </Text>
          )}
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>CPU</Text>
          <Text style={styles.statValue}>
            {metrics.cpu.processUsage ? metrics.cpu.processUsage.toFixed(1) : metrics.cpu.usage.toFixed(1)}%
          </Text>
          <Text style={styles.statPercentage}>{metrics.cpu.cores} cores</Text>
          {metrics.cpu.systemUsage && (
            <Text style={styles.statDetail}>
              System: {metrics.cpu.systemUsage.toFixed(1)}%
            </Text>
          )}
        </View>
        <View style={styles.statItem}>
          <Text style={styles.statLabel}>Battery</Text>
          <Text style={styles.statValue}>{metrics.energy.batteryLevel}%</Text>
          <Text style={styles.statPercentage}>
            {metrics.energy.isCharging ? '⚡' : '🔋'}
          </Text>
        </View>
      </View>

      {/* Expanded Details */}
      {isExpanded && (
        <View style={styles.expandedContent}>
          <ScrollView style={styles.detailsScroll} showsVerticalScrollIndicator={false}>
            {/* Memory Details */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Memory Details</Text>
              <View style={styles.detailGrid}>
                <DetailItem label="System Used" value={formatMemory(metrics.memory.used)} />
                <DetailItem label="System Free" value={formatMemory(metrics.memory.available)} />
                {metrics.memory.processResidentMB && (
                  <DetailItem label="Process Resident" value={formatMemory(metrics.memory.processResidentMB)} />
                )}
                {metrics.memory.nativeHeapAllocatedMB && (
                  <DetailItem label="Native Heap" value={formatMemory(metrics.memory.nativeHeapAllocatedMB)} />
                )}
                {metrics.memory.javaHeapUsedMB && (
                  <DetailItem label="Java Heap" value={formatMemory(metrics.memory.javaHeapUsedMB)} />
                )}
                {metrics.memory.processPssMB && (
                  <DetailItem label="Process PSS" value={formatMemory(metrics.memory.processPssMB)} />
                )}
                {metrics.memory.nativeLibrariesMB && (
                  <DetailItem label="Native Libraries" value={formatMemory(metrics.memory.nativeLibrariesMB)} />
                )}
                {metrics.memory.memoryPressure && (
                  <DetailItem
                    label="Memory Pressure"
                    value={`${metrics.memory.memoryPressure.toFixed(0)}%`}
                    valueStyle={metrics.memory.memoryPressure > 80 ? { color: '#ff6b6b' } : { color: '#4ecdc4' }}
                  />
                )}
              </View>
            </View>

            {/* CPU Details */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>CPU Details</Text>
              <View style={styles.detailGrid}>
                <DetailItem
                  label="Process Usage"
                  value={`${(metrics.cpu.processUsage || metrics.cpu.usage).toFixed(1)}%`}
                />
                {metrics.cpu.systemUsage && (
                  <DetailItem label="System Usage" value={`${metrics.cpu.systemUsage.toFixed(1)}%`} />
                )}
                <DetailItem label="Cores" value={metrics.cpu.cores} />
                {metrics.cpu.threadCount && (
                  <DetailItem label="Threads" value={metrics.cpu.threadCount} />
                )}
                {metrics.cpu.frequencyMHz && (
                  <DetailItem label="Frequency" value={`${metrics.cpu.frequencyMHz}MHz`} />
                )}
                {metrics.cpu.loadAvg1min && (
                  <DetailItem label="Load Avg (1m)" value={metrics.cpu.loadAvg1min.toFixed(2)} />
                )}
              </View>
            </View>

            {/* Process Details */}
            {metrics.process && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Process Details</Text>
                <View style={styles.detailGrid}>
                  <DetailItem label="PID" value={metrics.process.pid} />
                  {metrics.process.threadCount && (
                    <DetailItem label="Threads" value={metrics.process.threadCount} />
                  )}
                  {metrics.process.uptimeSeconds && (
                    <DetailItem label="Uptime" value={`${Math.round(metrics.process.uptimeSeconds / 60)}m`} />
                  )}
                </View>
              </View>
            )}

            {/* Energy Details */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Energy Details</Text>
              <View style={styles.detailGrid}>
                <DetailItem label="Battery" value={`${metrics.energy.batteryLevel}%`} />
                <DetailItem label="State" value={metrics.energy.powerState || 'unknown'} />
                <DetailItem label="Charging" value={metrics.energy.isCharging ? 'Yes' : 'No'} />
                {metrics.energy.lowPowerMode !== undefined && (
                  <DetailItem label="Low Power" value={metrics.energy.lowPowerMode ? 'Yes' : 'No'} />
                )}
                {metrics.energy.thermalState && (
                  <DetailItem label="Thermal" value={metrics.energy.thermalState} />
                )}
              </View>
            </View>
          </ScrollView>

          {/* Controls */}
          <View style={styles.controls}>
            <TouchableOpacity
              style={[styles.controlButton, isMonitoring && styles.controlButtonActive]}
              onPress={toggleMonitoring}
            >
              <Icon
                name={isMonitoring ? "stop" : "play"}
                size={14}
                color={isMonitoring ? "#FFFFFF" : "#3B82F6"}
              />
              <Text style={[styles.controlButtonText, isMonitoring && styles.controlButtonTextActive]}>
                {isMonitoring ? 'Stop' : 'Start'} Monitoring
              </Text>
            </TouchableOpacity>

            <View style={styles.controlButtonRow}>
              <TouchableOpacity style={styles.controlButtonSecondary} onPress={exportData}>
                <Icon name="download" size={12} color="#059669" />
                <Text style={styles.controlButtonSecondaryText}>Export</Text>
              </TouchableOpacity>

              <TouchableOpacity style={styles.controlButtonSecondary} onPress={clearData}>
                <Icon name="trash" size={12} color="#DC2626" />
                <Text style={styles.controlButtonSecondaryText}>Clear</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

// Memory delta display for tracking model loading impact
export const MemoryDeltaDisplay = ({ style }) => {
  const [summary, setSummary] = useState(null);

  useEffect(() => {
    const unsubscribe = PerformanceMonitor.subscribe(() => {
      setSummary(PerformanceMonitor.getSummary());
    });

    setSummary(PerformanceMonitor.getSummary());
    return unsubscribe;
  }, []);

  if (!summary || !summary.delta) {
    return null;
  }

  const { delta } = summary;
  const isSignificantChange = Math.abs(delta.used) > 10; // 10MB threshold

  return (
    <View style={[styles.deltaContainer, style]}>
      <View style={styles.deltaHeader}>
        <Icon name="line-chart" size={14} color="#3B82F6" />
        <Text style={styles.deltaTitle}>Memory Impact</Text>
      </View>
      <View style={styles.deltaStats}>
        <View style={[styles.deltaItem, isSignificantChange && styles.deltaItemSignificant]}>
          <Text style={styles.deltaLabel}>Used</Text>
          <Text style={[styles.deltaValue, delta.used > 0 && styles.deltaValueIncrease]}>
            {delta.used > 0 ? '+' : ''}{delta.used.toFixed(0)}MB
          </Text>
        </View>
        <View style={styles.deltaItem}>
          <Text style={styles.deltaLabel}>Available</Text>
          <Text style={[styles.deltaValue, delta.available < 0 && styles.deltaValueDecrease]}>
            {delta.available > 0 ? '+' : ''}{delta.available.toFixed(0)}MB
          </Text>
        </View>
      </View>
      {isSignificantChange && (
        <Text style={styles.deltaWarning}>
          ⚠️ Significant memory change detected
        </Text>
      )}
    </View>
  );
};

// Detail item component
const DetailItem = ({ label, value, valueStyle }) => (
  <View style={styles.detailItem}>
    <Text style={styles.detailLabel}>{label}</Text>
    <Text style={[styles.detailValue, valueStyle]}>{value}</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    margin: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F1F5F9',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1E293B',
  },
  monitoringIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#10B981',
  },
  loadingText: {
    padding: 16,
    textAlign: 'center',
    color: '#64748B',
    fontSize: 14,
  },
  quickStats: {
    flexDirection: 'row',
    padding: 12,
    gap: 12,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 11,
    color: '#64748B',
    marginBottom: 2,
  },
  statValue: {
    fontSize: 13,
    fontWeight: '600',
    color: '#1E293B',
  },
  statPercentage: {
    fontSize: 10,
    color: '#94A3B8',
  },
  statDetail: {
    fontSize: 9,
    color: '#64748B',
    marginTop: 2,
  },
  expandedContent: {
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  },
  detailsScroll: {
    maxHeight: 200,
    padding: 12,
  },
  section: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  detailGrid: {
    gap: 4,
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 2,
  },
  detailLabel: {
    fontSize: 11,
    color: '#64748B',
  },
  detailValue: {
    fontSize: 11,
    fontWeight: '500',
    color: '#1E293B',
  },
  controls: {
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  },
  controlButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    padding: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#3B82F6',
  },
  controlButtonActive: {
    backgroundColor: '#3B82F6',
  },
  controlButtonText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#3B82F6',
  },
  controlButtonTextActive: {
    color: '#FFFFFF',
  },
  controlButtonRow: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  controlButtonSecondary: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
    padding: 6,
    borderRadius: 6,
    backgroundColor: '#F8FAFC',
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  controlButtonSecondaryText: {
    fontSize: 11,
    fontWeight: '500',
    color: '#374151',
  },
  deltaContainer: {
    backgroundColor: '#F8FAFC',
    borderRadius: 8,
    padding: 12,
    margin: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  deltaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 8,
  },
  deltaTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
  },
  deltaStats: {
    flexDirection: 'row',
    gap: 16,
  },
  deltaItem: {
    alignItems: 'center',
  },
  deltaItemSignificant: {
    backgroundColor: '#FEF3C7',
    borderRadius: 6,
    padding: 4,
  },
  deltaLabel: {
    fontSize: 10,
    color: '#64748B',
    marginBottom: 2,
  },
  deltaValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1E293B',
  },
  deltaValueIncrease: {
    color: '#DC2626',
  },
  deltaValueDecrease: {
    color: '#059669',
  },
  deltaWarning: {
    fontSize: 10,
    color: '#D97706',
    marginTop: 6,
    textAlign: 'center',
  },
});
