<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 2925.248046875 759" style="max-width: 2925.248046875px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b"><style>#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .error-icon{fill:#a44141;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .error-text{fill:#ddd;stroke:#ddd;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edge-thickness-normal{stroke-width:1px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edge-thickness-thick{stroke-width:3.5px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edge-pattern-solid{stroke-dasharray:0;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .marker.cross{stroke:lightgrey;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b p{margin:0;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .cluster-label text{fill:#F9FFFE;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .cluster-label span{color:#F9FFFE;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .cluster-label span p{background-color:transparent;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .label text,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b span{fill:#ccc;color:#ccc;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node rect,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node circle,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node ellipse,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node polygon,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .rough-node .label text,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node .label text,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .image-shape .label,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .icon-shape .label{text-anchor:middle;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .rough-node .label,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node .label,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .image-shape .label,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .icon-shape .label{text-align:center;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .node.clickable{cursor:pointer;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .arrowheadPath{fill:lightgrey;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .cluster text{fill:#F9FFFE;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .cluster span{color:#F9FFFE;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b rect.text{fill:none;stroke-width:0;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .icon-shape,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .icon-shape p,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .icon-shape rect,#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .uiLayer&gt;*{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .uiLayer span{fill:#e1f5fe!important;stroke:#01579b!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .coreLayer&gt;*{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .coreLayer span{fill:#f3e5f5!important;stroke:#4a148c!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .aiLayer&gt;*{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .aiLayer span{fill:#e8f5e8!important;stroke:#1b5e20!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .nativeLayer&gt;*{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .nativeLayer span{fill:#fff3e0!important;stroke:#e65100!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .rustLayer&gt;*{fill:#ffebee!important;stroke:#b71c1c!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .rustLayer span{fill:#ffebee!important;stroke:#b71c1c!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .pythonLayer&gt;*{fill:#e0f2f1!important;stroke:#004d40!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .pythonLayer span{fill:#e0f2f1!important;stroke:#004d40!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .dataLayer&gt;*{fill:#fafafa!important;stroke:#424242!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .dataLayer span{fill:#fafafa!important;stroke:#424242!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .externalLayer&gt;*{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b .externalLayer span{fill:#f1f8e9!important;stroke:#33691e!important;stroke-width:2px!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph7" class="cluster"><rect height="306" width="273.5390625" y="291" x="8" style=""></rect><g transform="translate(72.99609375, 291)" class="cluster-label"><foreignObject height="24" width="143.546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌐 External Services</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="104" width="1867.037109375" y="647" x="210.8671875" style=""></rect><g transform="translate(1088.0771484375, 647)" class="cluster-label"><foreignObject height="24" width="112.6171875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>💾 Data Storage</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="435" width="496.66015625" y="162" x="301.5390625" style=""></rect><g transform="translate(450.708984375, 162)" class="cluster-label"><foreignObject height="24" width="198.3203125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🔧 Data Processing (Python)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="128" width="738.98046875" y="469" x="1769.572265625" style=""></rect><g transform="translate(2048.61328125, 469)" class="cluster-label"><foreignObject height="24" width="180.8984375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>⚡ Native Libraries (Rust)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="104" width="800.15625" y="647" x="2117.091796875" style=""></rect><g transform="translate(2455.314453125, 647)" class="cluster-label"><foreignObject height="24" width="123.7109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🌉 Native Bridges</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="128" width="910.228515625" y="469" x="839.34375" style=""></rect><g transform="translate(1224.5517578125, 469)" class="cluster-label"><foreignObject height="24" width="139.8125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🤖 AI/ML Processing</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="257" width="1789.142578125" y="162" x="964.34375" style=""></rect><g transform="translate(1767.3486328125, 162)" class="cluster-label"><foreignObject height="24" width="183.1328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>🧠 Core Application Layer</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="104" width="965.6953125" y="8" x="1858.9140625" style=""></rect><g transform="translate(2241.76171875, 8)" class="cluster-label"><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>📱 Mobile App (React Native)</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UI_App_0" d="M1974.949,87L1974.949,91.167C1974.949,95.333,1974.949,103.667,1974.949,112C1974.949,120.333,1974.949,128.667,1974.949,137C1974.949,145.333,1974.949,153.667,2047.804,164.586C2120.659,175.506,2266.368,189.011,2339.223,195.764L2412.078,202.517"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Chat_App_1" d="M2187.652,87L2187.652,91.167C2187.652,95.333,2187.652,103.667,2187.652,112C2187.652,120.333,2187.652,128.667,2187.652,137C2187.652,145.333,2187.652,153.667,2225.061,163.418C2262.47,173.17,2337.287,184.339,2374.696,189.924L2412.104,195.509"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ModelSelect_App_2" d="M2405.891,87L2405.891,91.167C2405.891,95.333,2405.891,103.667,2405.891,112C2405.891,120.333,2405.891,128.667,2405.891,137C2405.891,145.333,2405.891,153.667,2415.694,161.753C2425.497,169.838,2445.104,177.677,2454.908,181.596L2464.711,185.515"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RAGStatus_App_3" d="M2666.035,87L2666.035,91.167C2666.035,95.333,2666.035,103.667,2666.035,112C2666.035,120.333,2666.035,128.667,2666.035,137C2666.035,145.333,2666.035,153.667,2656.232,161.753C2646.428,169.838,2626.821,177.677,2617.018,181.596L2607.215,185.515"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_App_RAGService_4" d="M2416.061,219.359L2242.124,227.132C2068.188,234.906,1720.316,250.453,1546.38,262.393C1372.443,274.333,1372.443,282.667,1372.443,292.333C1372.443,302,1372.443,313,1372.443,318.5L1372.443,324"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_App_LLaMA_5" d="M2416.061,225.014L2341.692,231.845C2267.323,238.676,2118.585,252.338,2044.216,263.336C1969.848,274.333,1969.848,282.667,1969.848,297.5C1969.848,312.333,1969.848,333.667,1969.848,355C1969.848,376.333,1969.848,397.667,1969.848,412.5C1969.848,427.333,1969.848,435.667,1907.854,444C1845.86,452.333,1721.872,460.667,1659.879,470.333C1597.885,480,1597.885,491,1597.885,496.5L1597.885,502"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_App_ModelConfig_6" d="M2530.771,241L2529.969,245.167C2529.168,249.333,2527.565,257.667,2526.764,266C2525.963,274.333,2525.963,282.667,2525.963,292.333C2525.963,302,2525.963,313,2525.963,318.5L2525.963,324"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RAGService_ONNX_7" d="M1299.404,367.699L1250.228,376.249C1201.051,384.799,1102.697,401.9,1053.521,414.617C1004.344,427.333,1004.344,435.667,1004.344,444C1004.344,452.333,1004.344,460.667,1004.344,468.333C1004.344,476,1004.344,483,1004.344,486.5L1004.344,490"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RAGService_Tokenizer_8" d="M1343.079,382L1336.373,388.167C1329.666,394.333,1316.253,406.667,1309.546,417C1302.84,427.333,1302.84,435.667,1302.84,444C1302.84,452.333,1302.84,460.667,1302.84,470.333C1302.84,480,1302.84,491,1302.84,496.5L1302.84,502"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RAGService_HNSWLib_9" d="M1445.482,370.715L1482.886,378.762C1520.29,386.81,1595.097,402.905,1632.501,415.119C1669.904,427.333,1669.904,435.667,1765.285,444C1860.666,452.333,2051.428,460.667,2146.809,470.333C2242.189,480,2242.189,491,2242.189,496.5L2242.189,502"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RAGService_RNFS_10" d="M1445.482,365.506L1507.468,374.421C1569.453,383.337,1693.424,401.169,1755.409,414.251C1817.395,427.333,1817.395,435.667,1975.084,444C2132.774,452.333,2448.154,460.667,2605.843,475.5C2763.533,490.333,2763.533,511.667,2763.533,533C2763.533,554.333,2763.533,575.667,2763.533,590.5C2763.533,605.333,2763.533,613.667,2763.533,622C2763.533,630.333,2763.533,638.667,2763.533,646.333C2763.533,654,2763.533,661,2763.533,664.5L2763.533,668"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LLaMA_Models_11" d="M1597.885,560L1597.885,566.167C1597.885,572.333,1597.885,584.667,1597.885,595C1597.885,605.333,1597.885,613.667,1597.885,622C1597.885,630.333,1597.885,638.667,1597.885,646.333C1597.885,654,1597.885,661,1597.885,664.5L1597.885,668"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ONNX_Embeddings_12" d="M1004.344,572L1004.344,576.167C1004.344,580.333,1004.344,588.667,1004.344,597C1004.344,605.333,1004.344,613.667,1004.344,622C1004.344,630.333,1004.344,638.667,996.725,646.698C989.107,654.73,973.869,662.46,966.251,666.325L958.632,670.19"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HNSWLib_HNSWIndex_13" d="M2140.538,560L2117.322,566.167C2094.105,572.333,2047.672,584.667,2024.455,595C2001.238,605.333,2001.238,613.667,2001.238,622C2001.238,630.333,2001.238,638.667,1997.189,646.549C1993.14,654.432,1985.041,661.864,1980.992,665.58L1976.942,669.296"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HNSWLib_iOSBridge_14" d="M2242.189,560L2242.189,566.167C2242.189,572.333,2242.189,584.667,2242.189,595C2242.189,605.333,2242.189,613.667,2242.189,622C2242.189,630.333,2242.189,638.667,2242.189,646.333C2242.189,654,2242.189,661,2242.189,664.5L2242.189,668"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HNSWLib_AndroidBridge_15" d="M2346.124,560L2369.862,566.167C2393.6,572.333,2441.076,584.667,2464.815,595C2488.553,605.333,2488.553,613.667,2488.553,622C2488.553,630.333,2488.553,638.667,2488.553,646.333C2488.553,654,2488.553,661,2488.553,664.5L2488.553,668"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RAGBuilder_EmbedDocs_16" d="M477.293,241L477.293,245.167C477.293,249.333,477.293,257.667,477.293,266C477.293,274.333,477.293,282.667,477.293,290.333C477.293,298,477.293,305,477.293,308.5L477.293,312"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EmbedDocs_HNSWBuilder_17" d="M382.38,394L372.24,398.167C362.1,402.333,341.82,410.667,331.679,419C321.539,427.333,321.539,435.667,321.539,444C321.539,452.333,321.539,460.667,568.045,474.614C814.551,488.561,1307.563,508.122,1554.069,517.903L1800.575,527.683"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EmbedDocs_Documents_18" d="M394.568,394L385.73,398.167C376.892,402.333,359.215,410.667,350.377,419C341.539,427.333,341.539,435.667,341.539,444C341.539,452.333,341.539,460.667,341.539,475.5C341.539,490.333,341.539,511.667,341.539,533C341.539,554.333,341.539,575.667,341.539,590.5C341.539,605.333,341.539,613.667,341.539,622C341.539,630.333,341.539,638.667,341.539,646.333C341.539,654,341.539,661,341.539,664.5L341.539,668"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EmbedDocs_ModelConverter_19" d="M483.846,394L484.546,398.167C485.246,402.333,486.647,410.667,487.347,419C488.047,427.333,488.047,435.667,488.047,444C488.047,452.333,488.047,460.667,488.047,470.333C488.047,480,488.047,491,488.047,496.5L488.047,502"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_HNSWBuilder_HNSWIndex_20" d="M1934.572,572L1934.572,576.167C1934.572,580.333,1934.572,588.667,1934.572,597C1934.572,605.333,1934.572,613.667,1934.572,622C1934.572,630.333,1934.572,638.667,1935.248,646.345C1935.923,654.024,1937.274,661.048,1937.949,664.56L1938.625,668.072"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EmbedDocs_Metadata_21" d="M573.124,394L583.363,398.167C593.601,402.333,614.078,410.667,624.316,419C634.555,427.333,634.555,435.667,634.555,444C634.555,452.333,634.555,460.667,634.555,475.5C634.555,490.333,634.555,511.667,634.555,533C634.555,554.333,634.555,575.667,634.555,590.5C634.555,605.333,634.555,613.667,634.555,622C634.555,630.333,634.555,638.667,634.555,646.333C634.555,654,634.555,661,634.555,664.5L634.555,668"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EmbedDocs_Embeddings_22" d="M607.293,382.65L635.777,388.708C664.262,394.767,721.23,406.883,749.715,417.108C778.199,427.333,778.199,435.667,778.199,444C778.199,452.333,778.199,460.667,778.199,475.5C778.199,490.333,778.199,511.667,778.199,533C778.199,554.333,778.199,575.667,778.199,590.5C778.199,605.333,778.199,613.667,778.199,622C778.199,630.333,778.199,638.667,787.492,646.742C796.785,654.816,815.371,662.633,824.664,666.541L833.957,670.449"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ModelDownload_HuggingFace_23" d="M144.77,382L144.77,388.167C144.77,394.333,144.77,406.667,144.77,417C144.77,427.333,144.77,435.667,144.77,444C144.77,452.333,144.77,460.667,144.77,470.333C144.77,480,144.77,491,144.77,496.5L144.77,502"></path><path marker-end="url(#mermaid-8bdf39fd-81b1-43d0-8c31-f8fe1467804b_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_App_ModelDownload_24" d="M2542.147,241L2543.101,245.167C2544.056,249.333,2545.964,257.667,2546.919,266C2547.873,274.333,2547.873,282.667,2164.984,297.031C1782.095,311.394,1016.316,331.789,633.427,341.986L250.538,352.183"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(1974.94921875, 60)" id="flowchart-UI-0" class="node default uiLayer"><rect height="54" width="162.0703125" y="-27" x="-81.03515625" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.03515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="102.0703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Interface</p></span></div></foreignObject></g></g><g transform="translate(2187.65234375, 60)" id="flowchart-Chat-1" class="node default uiLayer"><rect height="54" width="163.3359375" y="-27" x="-81.66796875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-51.66796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="103.3359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Chat Interface</p></span></div></foreignObject></g></g><g transform="translate(2405.890625, 60)" id="flowchart-ModelSelect-2" class="node default uiLayer"><rect height="54" width="173.140625" y="-27" x="-86.5703125" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-56.5703125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="113.140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Model Selection</p></span></div></foreignObject></g></g><g transform="translate(2666.03515625, 60)" id="flowchart-RAGStatus-3" class="node default uiLayer"><rect height="54" width="247.1484375" y="-27" x="-123.57421875" style="fill:#e1f5fe !important;stroke:#01579b !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-93.57421875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="187.1484375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Status &amp; Debug Panel</p></span></div></foreignObject></g></g><g transform="translate(2535.962890625, 214)" id="flowchart-App-4" class="node default coreLayer"><rect height="54" width="239.8046875" y="-27" x="-119.90234375" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-89.90234375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="179.8046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>App.tsx - Main Controller</p></span></div></foreignObject></g></g><g transform="translate(1372.443359375, 355)" id="flowchart-RAGService-5" class="node default coreLayer"><rect height="54" width="146.078125" y="-27" x="-73.0390625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-43.0390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="86.078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Service</p></span></div></foreignObject></g></g><g transform="translate(1871.4140625, 355)" id="flowchart-RAGUtils-6" class="node default coreLayer"><rect height="54" width="126.8671875" y="-27" x="-63.43359375" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-33.43359375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="66.8671875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Utils</p></span></div></foreignObject></g></g><g transform="translate(2525.962890625, 355)" id="flowchart-ModelConfig-7" class="node default coreLayer"><rect height="54" width="204.125" y="-27" x="-102.0625" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-72.0625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="144.125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Model Configuration</p></span></div></foreignObject></g></g><g transform="translate(2096.02734375, 355)" id="flowchart-RAGComponents-8" class="node default coreLayer"><rect height="54" width="182.359375" y="-27" x="-91.1796875" style="fill:#f3e5f5 !important;stroke:#4a148c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-61.1796875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="122.359375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Components</p></span></div></foreignObject></g></g><g transform="translate(1597.884765625, 533)" id="flowchart-LLaMA-9" class="node default aiLayer"><rect height="54" width="233.375" y="-27" x="-116.6875" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-86.6875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="173.375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>LLaMA.rn - LLM Runtime</p></span></div></foreignObject></g></g><g transform="translate(1004.34375, 533)" id="flowchart-ONNX-10" class="node default aiLayer"><rect height="78" width="260" y="-39" x="-130" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ONNX Runtime - Embeddings</p></span></div></foreignObject></g></g><g transform="translate(1302.83984375, 533)" id="flowchart-Tokenizer-11" class="node default aiLayer"><rect height="54" width="236.9921875" y="-27" x="-118.49609375" style="fill:#e8f5e8 !important;stroke:#1b5e20 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-88.49609375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="176.9921875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Local/Bundled Tokenizer</p></span></div></foreignObject></g></g><g transform="translate(2242.189453125, 699)" id="flowchart-iOSBridge-12" class="node default nativeLayer"><rect height="54" width="180.1953125" y="-27" x="-90.09765625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-60.09765625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="120.1953125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>iOS HNSW Bridge</p></span></div></foreignObject></g></g><g transform="translate(2488.552734375, 699)" id="flowchart-AndroidBridge-13" class="node default nativeLayer"><rect height="54" width="212.53125" y="-27" x="-106.265625" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-76.265625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="152.53125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Android HNSW Bridge</p></span></div></foreignObject></g></g><g transform="translate(2763.533203125, 699)" id="flowchart-RNFS-14" class="node default nativeLayer"><rect height="54" width="237.4296875" y="-27" x="-118.71484375" style="fill:#fff3e0 !important;stroke:#e65100 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-88.71484375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="177.4296875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>React Native File System</p></span></div></foreignObject></g></g><g transform="translate(2242.189453125, 533)" id="flowchart-HNSWLib-15" class="node default rustLayer"><rect height="54" width="255.234375" y="-27" x="-127.6171875" style="fill:#ffebee !important;stroke:#b71c1c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-97.6171875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="195.234375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>hnsw_lib.rs - Vector Search</p></span></div></foreignObject></g></g><g transform="translate(1934.572265625, 533)" id="flowchart-HNSWBuilder-16" class="node default rustLayer"><rect height="78" width="260" y="-39" x="-130" style="fill:#ffebee !important;stroke:#b71c1c !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>hnsw_builder - Index Creation</p></span></div></foreignObject></g></g><g transform="translate(477.29296875, 355)" id="flowchart-EmbedDocs-17" class="node default pythonLayer"><rect height="78" width="260" y="-39" x="-130" style="fill:#e0f2f1 !important;stroke:#004d40 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-100, -24)" style="" class="label"><rect></rect><foreignObject height="48" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>embed_docs.py - Document Processing</p></span></div></foreignObject></g></g><g transform="translate(477.29296875, 214)" id="flowchart-RAGBuilder-18" class="node default pythonLayer"><rect height="54" width="207.2734375" y="-27" x="-103.63671875" style="fill:#e0f2f1 !important;stroke:#004d40 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-73.63671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="147.2734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RAG Builder Pipeline</p></span></div></foreignObject></g></g><g transform="translate(488.046875, 533)" id="flowchart-ModelConverter-19" class="node default pythonLayer"><rect height="54" width="223.015625" y="-27" x="-111.5078125" style="fill:#e0f2f1 !important;stroke:#004d40 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-81.5078125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="163.015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>ONNX Model Converter</p></span></div></foreignObject></g></g><g transform="translate(1597.884765625, 699)" id="flowchart-Models-20" class="node default dataLayer"><rect height="54" width="154.0078125" y="-27" x="-77.00390625" style="fill:#fafafa !important;stroke:#424242 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-47.00390625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="94.0078125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>GGUF Models</p></span></div></foreignObject></g></g><g transform="translate(1944.572265625, 699)" id="flowchart-HNSWIndex-21" class="node default dataLayer"><rect height="54" width="196.6640625" y="-27" x="-98.33203125" style="fill:#fafafa !important;stroke:#424242 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-68.33203125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="136.6640625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>HNSW Vector Index</p></span></div></foreignObject></g></g><g transform="translate(634.5546875, 699)" id="flowchart-Metadata-22" class="node default dataLayer"><rect height="54" width="203.875" y="-27" x="-101.9375" style="fill:#fafafa !important;stroke:#424242 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-71.9375, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Document Metadata</p></span></div></foreignObject></g></g><g transform="translate(901.84375, 699)" id="flowchart-Embeddings-23" class="node default dataLayer"><rect height="54" width="230.703125" y="-27" x="-115.3515625" style="fill:#fafafa !important;stroke:#424242 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-85.3515625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="170.703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Embedding Model ONNX</p></span></div></foreignObject></g></g><g transform="translate(341.5390625, 699)" id="flowchart-Documents-24" class="node default dataLayer"><rect height="54" width="191.34375" y="-27" x="-95.671875" style="fill:#fafafa !important;stroke:#424242 !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-65.671875, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="131.34375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Source Documents</p></span></div></foreignObject></g></g><g transform="translate(144.76953125, 533)" id="flowchart-HuggingFace-25" class="node default externalLayer"><rect height="54" width="187.828125" y="-27" x="-93.9140625" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-63.9140625, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="127.828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Hugging Face Hub</p></span></div></foreignObject></g></g><g transform="translate(144.76953125, 355)" id="flowchart-ModelDownload-26" class="node default externalLayer"><rect height="54" width="203.5390625" y="-27" x="-101.76953125" style="fill:#f1f8e9 !important;stroke:#33691e !important;stroke-width:2px !important" class="basic label-container"></rect><g transform="translate(-71.76953125, -12)" style="" class="label"><rect></rect><foreignObject height="24" width="143.5390625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Model Download API</p></span></div></foreignObject></g></g></g></g></g></svg>