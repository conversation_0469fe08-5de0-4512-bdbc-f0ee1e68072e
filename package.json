{"name": "SmartVA", "version": "0.0.1", "private": true, "onnxruntimeEnableExtensions": "true", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-documents/picker": "^10.1.3", "axios": "^1.7.9", "hnswlib-wasm": "^0.8.2", "llama.rn": "^0.5.0", "lucide-react-native": "^0.474.0", "onnxruntime-react-native": "^1.21.0", "react": "18.3.1", "react-native": "0.77.0", "react-native-device-info": "^14.0.4", "react-native-fs": "^2.20.0", "react-native-markdown-display": "^7.0.2", "react-native-vector-icons": "^10.2.0", "react-syntax-highlighter": "^15.6.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.1", "@react-native-community/cli-platform-android": "15.0.1", "@react-native-community/cli-platform-ios": "15.0.1", "@react-native/babel-preset": "0.77.0", "@react-native/eslint-config": "0.77.0", "@react-native/metro-config": "0.77.0", "@react-native/typescript-config": "0.77.0", "@types/jest": "^29.5.13", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-plugin-transform-import-meta": "^2.3.2", "eslint": "^8.19.0", "jest": "^29.6.3", "patch-package": "^8.0.0", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}