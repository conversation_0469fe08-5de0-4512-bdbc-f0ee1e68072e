{"rustc": 15497389221046826682, "features": "[\"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 2040997289075261528, "path": 307337821104044350, "deps": [[555019317135488525, "regex_automata", false, 7469500295744626973], [2779309023524819297, "aho_corasick", false, 12295269210952398225], [3129130049864710036, "memchr", false, 14608327850290828109], [9408802513701742484, "regex_syntax", false, 17384229768737248317]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/regex-840e83e9c34a1c1c/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}