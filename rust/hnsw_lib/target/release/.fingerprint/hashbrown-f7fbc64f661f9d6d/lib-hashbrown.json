{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 2040997289075261528, "path": 4258102334191925068, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hashbrown-f7fbc64f661f9d6d/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}