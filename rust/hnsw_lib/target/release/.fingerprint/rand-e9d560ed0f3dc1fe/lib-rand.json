{"rustc": 15497389221046826682, "features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"rand_chacha\", \"std\", \"std_rng\"]", "declared_features": "[\"alloc\", \"default\", \"getrandom\", \"libc\", \"log\", \"min_const_gen\", \"nightly\", \"packed_simd\", \"rand_chacha\", \"serde\", \"serde1\", \"simd_support\", \"small_rng\", \"std\", \"std_rng\"]", "target": 8827111241893198906, "profile": 2040997289075261528, "path": 9132151502926973543, "deps": [[1573238666360410412, "rand_chacha", false, 4773584325948017661], [2924422107542798392, "libc", false, 13598210507830195814], [18130209639506977569, "rand_core", false, 10676119263356461745]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/rand-e9d560ed0f3dc1fe/dep-lib-rand", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}