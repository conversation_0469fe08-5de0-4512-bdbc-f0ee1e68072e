use std::{
    ffi::CStr,
    os::raw::{c_char, c_int, c_float},
    path::Path,
    sync::{Mute<PERSON>, LazyLock},
    collections::HashMap,
};

use hnsw_rs::{
    hnswio::{HnswIo, ReloadOptions},
    hnsw::Hnsw,
    prelude::DistCosine,
    api::AnnT,
};

const OK: c_int = 0;
const ERR_SEARCH: c_int = 1;
const ERR_BUILD: c_int = 2;
const ERR_SAVE: c_int = 3;
const ERR_LOAD: c_int = 4;

// Global cache for loaded indices to avoid reloading from disk
static INDEX_CACHE: LazyLock<Mutex<HashMap<String, Hnsw<f32, DistCosine>>>> =
    LazyLock::new(|| Mutex::new(HashMap::new()));

/// Search the HNSW index on disk (reloads from disk each time).
/// 
/// - `query_ptr`: pointer to float array of size `dim`
/// - `dim`: length of the vector
/// - `k`: top-k neighbors to retrieve
/// - `out_ptr`: pointer to array of `k` c_ints to write result IDs
/// - `dir_ptr`: path to index directory
/// - `base_ptr`: base filename (e.g. "index" for "index.hnsw.data")
#[unsafe(no_mangle)]
pub unsafe extern "C" fn hnsw_search(
    query_ptr: *const c_float,
    dim: usize,
    k: usize,
    out_ptr: *mut c_int,
    dir_ptr: *const c_char,
    base_ptr: *const c_char,
) -> c_int {
    if query_ptr.is_null() || out_ptr.is_null() || dir_ptr.is_null() || base_ptr.is_null() {
        return ERR_SEARCH;
    }

    let dir_cstr = unsafe { CStr::from_ptr(dir_ptr) };
    let base_cstr = unsafe { CStr::from_ptr(base_ptr) };

    let dir_str = match dir_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_SEARCH,
    };
    let base_str = match base_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_SEARCH,
    };

    let dir_path = Path::new(dir_str);

    let mut io = HnswIo::new(&dir_path, base_str);
    io.set_options(ReloadOptions::default().set_mmap(true));

    let index: Hnsw<f32, DistCosine> = match io.load_hnsw::<f32, DistCosine>() {
        Ok(idx) => idx,
        Err(_) => return ERR_SEARCH,
    };

    let query = unsafe { std::slice::from_raw_parts(query_ptr, dim).to_vec() };

    let ef = index.get_ef_construction().max(k);
    let results = index.search(&query, k, ef);

    for (i, neighbor) in results.iter().take(k).enumerate() {
        unsafe {
            *out_ptr.add(i) = neighbor.d_id as c_int;
        }
    }

    OK
}

/// Build a new HNSW index from embeddings and save to disk.
///
/// - `embeddings_ptr`: pointer to flattened float array (num_docs * dim elements)
/// - `num_docs`: number of documents/embeddings
/// - `dim`: dimensionality of each embedding
/// - `dir_ptr`: path to output directory
/// - `base_ptr`: base filename for the index
/// - `m`: number of bi-directional links per node (default 16)
/// - `ef_construction`: candidate list size during building (default 200)
#[unsafe(no_mangle)]
pub unsafe extern "C" fn hnsw_build_index(
    embeddings_ptr: *const c_float,
    num_docs: usize,
    dim: usize,
    dir_ptr: *const c_char,
    base_ptr: *const c_char,
    m: usize,
    ef_construction: usize,
) -> c_int {
    if embeddings_ptr.is_null() || dir_ptr.is_null() || base_ptr.is_null() {
        return ERR_BUILD;
    }

    let dir_cstr = unsafe { CStr::from_ptr(dir_ptr) };
    let base_cstr = unsafe { CStr::from_ptr(base_ptr) };

    let dir_str = match dir_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_BUILD,
    };
    let base_str = match base_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_BUILD,
    };

    let dir_path = Path::new(dir_str);

    // Create the HNSW index
    let max_layer = 16;
    let hnsw: Hnsw<f32, DistCosine> = Hnsw::new(
        m,
        num_docs,
        max_layer,
        ef_construction,
        DistCosine {},
    );

    // Insert all embeddings
    let embeddings_slice = unsafe { std::slice::from_raw_parts(embeddings_ptr, num_docs * dim) };

    for doc_id in 0..num_docs {
        let start_idx = doc_id * dim;
        let end_idx = start_idx + dim;
        let embedding = &embeddings_slice[start_idx..end_idx];

        hnsw.insert((embedding, doc_id));
    }

    // Save the index to disk
    if let Err(_) = hnsw.file_dump(dir_path, base_str) {
        return ERR_SAVE;
    }

    OK
}

/// Add new embeddings to an existing HNSW index.
///
/// - `embeddings_ptr`: pointer to flattened float array (num_new_docs * dim elements)
/// - `num_new_docs`: number of new documents/embeddings to add
/// - `dim`: dimensionality of each embedding
/// - `dir_ptr`: path to index directory
/// - `base_ptr`: base filename for the index
/// - `start_id`: starting ID for new documents (should be > existing max ID)
#[unsafe(no_mangle)]
pub unsafe extern "C" fn hnsw_add_to_index(
    embeddings_ptr: *const c_float,
    num_new_docs: usize,
    dim: usize,
    dir_ptr: *const c_char,
    base_ptr: *const c_char,
    start_id: usize,
) -> c_int {
    if embeddings_ptr.is_null() || dir_ptr.is_null() || base_ptr.is_null() {
        return ERR_BUILD;
    }

    let dir_cstr = unsafe { CStr::from_ptr(dir_ptr) };
    let base_cstr = unsafe { CStr::from_ptr(base_ptr) };

    let dir_str = match dir_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_BUILD,
    };
    let base_str = match base_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return ERR_BUILD,
    };

    let dir_path = Path::new(dir_str);

    // Load existing index from disk
    let mut io = HnswIo::new(&dir_path, base_str);
    io.set_options(ReloadOptions::default().set_mmap(false)); // Don't use mmap for modification

    let mut hnsw = match io.load_hnsw::<f32, DistCosine>() {
        Ok(idx) => idx,
        Err(_) => return ERR_LOAD,
    };

    // Add new embeddings
    let embeddings_slice = unsafe { std::slice::from_raw_parts(embeddings_ptr, num_new_docs * dim) };

    for i in 0..num_new_docs {
        let doc_id = start_id + i;
        let start_idx = i * dim;
        let end_idx = start_idx + dim;
        let embedding = &embeddings_slice[start_idx..end_idx];

        hnsw.insert((embedding, doc_id));
    }

    // Save the updated index
    if let Err(_) = hnsw.file_dump(dir_path, base_str) {
        return ERR_SAVE;
    }

    OK
}

/// Get the number of documents in an existing HNSW index.
///
/// - `dir_ptr`: path to index directory
/// - `base_ptr`: base filename for the index
///
/// Returns the number of documents, or -1 on error.
#[unsafe(no_mangle)]
pub unsafe extern "C" fn hnsw_get_doc_count(
    dir_ptr: *const c_char,
    base_ptr: *const c_char,
) -> c_int {
    if dir_ptr.is_null() || base_ptr.is_null() {
        return -1;
    }

    let dir_cstr = unsafe { CStr::from_ptr(dir_ptr) };
    let base_cstr = unsafe { CStr::from_ptr(base_ptr) };

    let dir_str = match dir_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return -1,
    };
    let base_str = match base_cstr.to_str() {
        Ok(s) => s,
        Err(_) => return -1,
    };

    let dir_path = Path::new(dir_str);

    // Load from disk
    let mut io = HnswIo::new(&dir_path, base_str);
    io.set_options(ReloadOptions::default().set_mmap(true));

    match io.load_hnsw::<f32, DistCosine>() {
        Ok(index) => index.get_nb_point() as c_int,
        Err(_) => -1,
    }
}

/// Clear the index cache to free memory.
#[unsafe(no_mangle)]
pub unsafe extern "C" fn hnsw_clear_cache() -> c_int {
    if let Ok(mut cache) = INDEX_CACHE.lock() {
        cache.clear();
        OK
    } else {
        ERR_LOAD
    }
}
