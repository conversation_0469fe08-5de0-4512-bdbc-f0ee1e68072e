{"rustc": 15497389221046826682, "features": "[\"ahash\", \"allocator-api2\", \"default\", \"inline-more\"]", "declared_features": "[\"ahash\", \"alloc\", \"allocator-api2\", \"compiler_builtins\", \"core\", \"default\", \"equivalent\", \"inline-more\", \"nightly\", \"raw\", \"rayon\", \"rkyv\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 9101038166729729440, "profile": 2040997289075261528, "path": 11990433733883447028, "deps": [[9150530836556604396, "allocator_api2", false, 18141264234433968157], [10791833957791020630, "ahash", false, 650577916614721330]], "local": [{"CheckDepInfo": {"dep_info": "release/.fingerprint/hashbrown-87feac9618b014f1/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}