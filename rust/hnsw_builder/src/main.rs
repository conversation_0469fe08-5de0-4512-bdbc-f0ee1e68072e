use clap::Parser;
use std::{error::Error, path::{PathBuf, Path}};
use csv::ReaderBuilder;
use hnsw_rs::prelude::{DistCosine};
use hnsw_rs::hnsw::{Hnsw};
use hnsw_rs::api::AnnT;

/// CLI for building and dumping an HNSW index from a CSV of embeddings.
#[derive(Parser)]
#[command(author, version, about, long_about = None)]
struct Args {
    /// Path to embeddings CSV file. First column: ID (usize), remaining columns: f32 components.
    #[arg(long)]
    embeddings_csv: PathBuf,
    /// Output path for the HNSW index (binary format hnsw_rs expects).
    #[arg(long)]
    output: PathBuf,
    /// "M" parameter: number of bi-directional links per node (default 16).
    #[arg(long, default_value_t = 16)]
    m: usize,
    /// "ef_construction" parameter: candidate list size during index building (default 200).
    #[arg(long, default_value_t = 200)]
    ef_construction: usize,
    /// Dimensionality of each embedding vector.
    #[arg(long)]
    dims: usize,
}

fn main() -> Result<(), Box<dyn Error>> {
    let args = Args::parse();
    // Read CSV: each row should have dims+1 columns (id + components)
    let mut rdr = ReaderBuilder::new()
        .has_headers(false)
        .from_path(&args.embeddings_csv)?;

    let mut embeddings: Vec<Vec<f32>> = Vec::new();
    let mut ids: Vec<usize> = Vec::new();

    for result in rdr.records() {
        let record = result?;
        if record.len() != args.dims + 1 {
            return Err(format!(
                "Expected {} dims + 1 columns, got {}",
                args.dims,
                record.len()
            )
            .into());
        }
        let id: usize = record[0].parse()?;
        let mut vec = Vec::with_capacity(args.dims);
        for i in 1..=args.dims {
            vec.push(record[i].parse()?);
        }
        ids.push(id);
        embeddings.push(vec);
    }

    // Build the HNSW index
    let max_elements = ids.len();
    let max_layer = 16;

    let hnsw: Hnsw<f32, DistCosine> = Hnsw::new(
    /* max_nb_connection */ args.m,
    /* max_elements    */ max_elements,
    /* max_layer       */ max_layer,
    /* ef_construction */ args.ef_construction,
    /* distance fn     */ DistCosine {},
    );
    for (vec, &id) in embeddings.iter().zip(&ids) {
        hnsw.insert((&vec.clone(), id));
    }

    // Dump using file_dump with a path and base filename
    let out_dir = args.output.parent().unwrap_or(Path::new("."));
    let basename = args
        .output
        .file_stem()
        .and_then(|s| s.to_str())
        .ok_or("Invalid output filename")?;
    hnsw.file_dump(out_dir, basename)?;
    println!("Index successfully dumped to {:?}", &args.output);
    Ok(())
}