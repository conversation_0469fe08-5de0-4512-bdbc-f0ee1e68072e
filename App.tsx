// App.tsx - Complete Implementation
import React, { useState, useRef, useEffect } from "react";
import {
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  View,
  Alert,
  TextInput,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from "react-native";
import Icon from 'react-native-vector-icons/FontAwesome';

import { initLlama, releaseAllLlama } from "llama.rn";
import { downloadModel } from "./src/api/model";
import ProgressBar from "./src/components/ProgressBar";
import RNFS from "react-native-fs";
import axios from 'axios/dist/browser/axios.cjs';

// Import our modular components and services
import EnhancedRAGService from './src/services/EnhancedRAGService';
import PerformanceMonitor from './src/services/PerformanceMonitor';
import {
  EnhancedMessageBubble,
  RAGStatusBar,
  DebugPanel
} from './src/components/RAGComponents';
import DocumentManager from './src/components/DocumentManager';
import {
  PerformanceMetricsDisplay,
  MemoryDeltaDisplay
} from './src/components/PerformanceComponents';
import {
  MODEL_FORMATS,
  HF_TO_GGUF,
  getOptimalChunkCount,
  getModelInfo,
  validateModelForRAG
} from './src/config/ModelConfig';
import RAGUtils from './src/utils/RAGUtils';

type Message = {
  role: "user" | "assistant" | "system";
  content: string;
  thought?: string;
  showThought?: boolean;
  context?: string;
  showContext?: boolean;
  retrievedDocs?: any[];
  queryTime?: number;
  cacheHits?: number;
  totalChunks?: number;
};

const INITIAL_CONVERSATION: Message[] = [
  {
    role: "system",
    content: "This is a conversation between user and assistant, a friendly chatbot. The assistant has access to a knowledge base and will use it to provide accurate information when available.",
  },
];

function App(): React.JSX.Element {
  // Core app state
  const [context, setContext] = useState<any>(null);
  const [conversation, setConversation] = useState<Message[]>(INITIAL_CONVERSATION);
  const [userInput, setUserInput] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [autoScrollEnabled, setAutoScrollEnabled] = useState(true);

  // Model management state
  const [selectedModelFormat, setSelectedModelFormat] = useState<string>("");
  const [selectedGGUF, setSelectedGGUF] = useState<string | null>(null);
  const [availableGGUFs, setAvailableGGUFs] = useState<string[]>([]);
  const [downloadedModels, setDownloadedModels] = useState<string[]>([]);
  const [progress, setProgress] = useState<number>(0);
  const [isDownloading, setIsDownloading] = useState<boolean>(false);
  const [isFetching, setIsFetching] = useState<boolean>(false);

  // Performance tracking
  const [tokensPerSecond, setTokensPerSecond] = useState<Array<{
    tokensPerSecond: number;
    ttft: number;
    ragTime: number;
    totalTokens: number;
  } | number>>([]);

  // Navigation state
  const [currentPage, setCurrentPage] = useState<"modelSelection" | "conversation">("modelSelection");

  // RAG state
  const [ragEnabled, setRagEnabled] = useState<boolean>(true);
  const [isLoadingRAG, setIsLoadingRAG] = useState<boolean>(false);
  const [ragStats, setRagStats] = useState(EnhancedRAGService.getStats());
  const [customDocCount, setCustomDocCount] = useState<number | null>(null);

  // Document manager state
  const [showDocumentManager, setShowDocumentManager] = useState<boolean>(false);

  // Refs
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollPositionRef = useRef(0);
  const contentHeightRef = useRef(0);

  // Initialize app
  useEffect(() => {
    initializeApp();
    return () => {
      if (context) {
        releaseAllLlama();
      }
      EnhancedRAGService.clearCaches();
    };
  }, []);

  // Update RAG stats periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setRagStats(EnhancedRAGService.getStats());
    }, 5000);
    return () => clearInterval(interval);
  }, []);

  // Check downloaded models when page changes
  useEffect(() => {
    if (currentPage === "modelSelection") {
      checkDownloadedModels();
    }
  }, [currentPage]);

  const initializeApp = async () => {
    await checkDownloadedModels();

    // Initialize performance monitoring
    try {
      console.log('🔧 Initializing performance monitoring...');
      await PerformanceMonitor.initialize();
      console.log('✅ Performance monitoring initialized');
    } catch (error) {
      console.warn('⚠️ Performance monitoring initialization failed:', error);
    }

    if (ragEnabled) {
      await initializeRAG();
    }
  };

  const initializeRAG = async () => {
    setIsLoadingRAG(true);
    try {
      console.log("🚀 Initializing RAG system...");
      const success = await EnhancedRAGService.initialize();
      
      if (success) {
        console.log("✅ RAG system initialized successfully");
        setRagStats(EnhancedRAGService.getStats());
        
        // Test the system
        setTimeout(async () => {
          try {
            await testRAGSystem();
          } catch (error) {
            console.warn("RAG test failed:", error);
          }
        }, 1000);
      } else {
        throw new Error("RAG initialization returned false");
      }
    } catch (error) {
      console.error("❌ RAG initialization failed:", error);
      Alert.alert(
        "RAG System Error",
        `Failed to initialize RAG system: ${(error as Error).message}\n\nYou can still use the chat without RAG features.`,
        [
          { text: "Disable RAG", onPress: () => setRagEnabled(false), style: "destructive" },
          { text: "Keep Trying", style: "default" }
        ]
      );
    } finally {
      setIsLoadingRAG(false);
    }
  };

  const testRAGSystem = async () => {
    try {
      console.log("🧪 Testing RAG system...");
      
      // Test search functionality
      const testQuery = "test query";
      const results = await EnhancedRAGService.querySimilarDocuments(testQuery, 3);
      console.log(`✅ Search test completed: ${results.documents.length} docs retrieved`);
      
    } catch (error) {
      console.warn("⚠️ RAG test failed:", error);
    }
  };

  const checkDownloadedModels = async () => {
    try {
      const files = await RNFS.readDir(RNFS.DocumentDirectoryPath);
      const ggufFiles = files
        .filter((file) => file.name.endsWith(".gguf"))
        .map((file) => file.name);
      setDownloadedModels(ggufFiles);
    } catch (error) {
      console.error("Error checking downloaded models:", error);
    }
  };

  const handleFormatSelection = (format: string) => {
    setSelectedModelFormat(format);
    setAvailableGGUFs([]);
    fetchAvailableGGUFs(format);
  };

  const fetchAvailableGGUFs = async (modelFormat: string) => {
    setIsFetching(true);
    try {
      const repoPath = HF_TO_GGUF[modelFormat as keyof typeof HF_TO_GGUF];
      const response = await axios.get(`https://huggingface.co/api/models/${repoPath}`);
      const files = response.data.siblings.filter((file: any) =>
        file.rfilename.endsWith(".gguf")
      );
      setAvailableGGUFs(files.map((file: any) => file.rfilename));
    } catch (error) {
      Alert.alert("Error", "Failed to fetch .gguf files from Hugging Face API.");
    } finally {
      setIsFetching(false);
    }
  };

  const handleGGUFSelection = (file: string) => {
    setSelectedGGUF(file);
    Alert.alert(
      "Confirm Download",
      `Do you want to download ${file}?`,
      [
        { text: "No", onPress: () => setSelectedGGUF(null), style: "cancel" },
        { text: "Yes", onPress: () => handleDownloadAndNavigate(file) },
      ]
    );
  };

  const handleDownloadAndNavigate = async (file: string) => {
    await handleDownloadModel(file);
    setCurrentPage("conversation");
  };

  const handleDownloadModel = async (file: string) => {
    const downloadUrl = `https://huggingface.co/${HF_TO_GGUF[selectedModelFormat as keyof typeof HF_TO_GGUF]}/resolve/main/${file}`;
    setIsDownloading(true);
    setProgress(0);

    const destPath = `${RNFS.DocumentDirectoryPath}/${file}`;
    
    // Check if file already exists
    if (await RNFS.exists(destPath)) {
      const success = await loadModel(file);
      if (success) {
        Alert.alert("Info", `File ${file} already exists, loading it directly.`);
        setIsDownloading(false);
        return;
      }
    }

    try {
      await downloadModel(file, downloadUrl, (progress) => setProgress(progress));
      Alert.alert("Success", `Model downloaded successfully`);
      await loadModel(file);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error", `Download failed: ${errorMessage}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const loadModel = async (modelName: string) => {
    try {
      const destPath = `${RNFS.DocumentDirectoryPath}/${modelName}`;

      if (context) {
        await releaseAllLlama();
        setContext(null);
        setConversation(INITIAL_CONVERSATION);
      }

      // Track model loading performance
      const { result: llamaContext } = await PerformanceMonitor.trackOperation(
        `Loading model: ${modelName}`,
        async () => {
          return await initLlama({
            model: destPath,
            use_mlock: true,
            n_ctx: 2048,
            n_gpu_layers: 1,
          });
        }
      );

      setContext(llamaContext);
      Alert.alert("Model Loaded", "The model was successfully loaded.");
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error Loading Model", errorMessage);
      return false;
    }
  };

  const handleBackToModelSelection = () => {
    setContext(null);
    releaseAllLlama();
    setConversation(INITIAL_CONVERSATION);
    setSelectedGGUF(null);
    setTokensPerSecond([]);
    setCustomDocCount(null);
    setCurrentPage("modelSelection");
  };

  const toggleRAG = () => {
    setRagEnabled(previous => !previous);
    if (!ragEnabled && !EnhancedRAGService.getStats().isInitialized) {
      // Re-initialize RAG if it was disabled and not initialized
      setTimeout(initializeRAG, 100);
    }
  };

  const toggleContext = (messageIndex: number) => {
    setConversation((prev) =>
      prev.map((msg, index) =>
        index === messageIndex ? { ...msg, showContext: !msg.showContext } : msg
      )
    );
  };

  const toggleThought = (messageIndex: number) => {
    setConversation((prev) =>
      prev.map((msg, index) =>
        index === messageIndex ? { ...msg, showThought: !msg.showThought } : msg
      )
    );
  };

  const handleSendMessage = async () => {
    if (!context) {
      Alert.alert("Model Not Loaded", "Please load the model first.");
      return;
    }
    if (!userInput.trim()) {
      Alert.alert("Input Error", "Please enter a message.");
      return;
    }

    const newConversation: Message[] = [
      ...conversation,
      { role: "user", content: userInput },
    ];
    setConversation(newConversation);
    setUserInput("");
    setIsLoading(true);
    setIsGenerating(true);
    setAutoScrollEnabled(true);

    try {
      // Enhanced RAG retrieval
      let retrievedContext = "";
      let retrievedDocs = [];
      let ragTiming = null;

      if (ragEnabled && EnhancedRAGService.getStats().isInitialized) {
        try {
          console.log("🔍 Starting RAG retrieval...");

          // Get chunk count - use custom count if set, otherwise use adaptive count
          const baseChunkCount = getOptimalChunkCount(selectedGGUF);
          const adaptiveChunkCount = customDocCount || RAGUtils.getAdaptiveChunkCount(userInput, baseChunkCount);

          // Query RAG system
          const ragResults = await EnhancedRAGService.querySimilarDocuments(userInput, adaptiveChunkCount);
          retrievedDocs = ragResults.documents;

          ragTiming = {
            queryTime: ragResults.queryTime,
            cacheHits: ragResults.cacheStats.hits,
            totalChunks: ragResults.cacheStats.hits + ragResults.cacheStats.misses
          };

          // Rank documents by relevance
          const rankedDocs = RAGUtils.rankDocumentsByRelevance(userInput, retrievedDocs);
          retrievedDocs = rankedDocs;

          if (retrievedDocs.length > 0) {
            retrievedContext = RAGUtils.formatRetrievedContext(retrievedDocs);
            console.log(`✅ RAG retrieval: ${retrievedDocs.length} docs in ${(ragTiming.queryTime / 1000).toFixed(2)}s`);

            // Smart prefetching for next query
            setTimeout(() => RAGUtils.smartPrefetch(newConversation), 1000);
          } else {
            console.log("⚠️ RAG retrieval returned no relevant documents");
          }

        } catch (ragError) {
          console.error("❌ RAG retrieval failed:", ragError);
          retrievedContext = "";
          retrievedDocs = [];
        }
      }

      const stopWords = [
        "</s>", "<|end|>", "user:", "assistant:", "<|im_end|>", 
        "<|eot_id|>", "<|end▁of▁sentence|>", "<|end_of_text|>", 
        "<｜end▁of▁sentence｜>"
      ];
      
      let chat = [...newConversation];

      // Create enhanced system message with RAG context
      if (retrievedContext && ragEnabled) {
        const enhancedSystemMessage = RAGUtils.createEnhancedSystemMessage(
          conversation[0].content, 
          retrievedContext
        );
        chat[0] = enhancedSystemMessage;
      }

      // Add enhanced assistant message placeholder
      setConversation((prev) => [
        ...prev,
        {
          role: "assistant",
          content: "",
          thought: undefined,
          showThought: false,
          context: retrievedContext,
          showContext: false,
          retrievedDocs: retrievedDocs,
          queryTime: ragTiming?.queryTime,
          cacheHits: ragTiming?.cacheHits,
          totalChunks: ragTiming?.totalChunks,
        },
      ]);

      // Generate response with TTFT tracking
      let currentAssistantMessage = "";
      let currentThought = "";
      let inThinkBlock = false;
      let firstTokenTime: number | null = null;
      let tokenCount = 0;
      const generationStartTime = Date.now();

      interface CompletionData {
        token: string;
      }

      interface CompletionResult {
        timings: {
          predicted_per_second: number;
        };
      }

      // Track the inference operation with enhanced monitoring
      const { result: inferenceResult, operationData }: { result: any, operationData: any } = await PerformanceMonitor.trackInference(
        async () => {
          return await context.completion(
            {
              messages: chat,
              n_predict: 10000,
              stop: stopWords,
            },
            (data: CompletionData) => {
              const token = data.token;
              tokenCount++;

              // Track TTFT (Time To First Token)
              if (firstTokenTime === null) {
                firstTokenTime = Date.now();
              }

          currentAssistantMessage += token;

          if (token.includes("<think>")) {
            inThinkBlock = true;
            currentThought = token.replace("<think>", "");
          } else if (token.includes("</think>")) {
            inThinkBlock = false;
            const finalThought = currentThought.replace("</think>", "").trim();

            setConversation((prev) => {
              const lastIndex = prev.length - 1;
              const updated = [...prev];
              updated[lastIndex] = {
                ...updated[lastIndex],
                content: updated[lastIndex].content.replace(
                  `<think>${finalThought}</think>`,
                  ""
                ),
                thought: finalThought,
              };
              return updated;
            });

            currentThought = "";
          } else if (inThinkBlock) {
            currentThought += token;
          }

          const visibleContent = currentAssistantMessage
            .replace(/<think>.*?<\/think>/g, "")
            .trim();

          setConversation((prev) => {
            const lastIndex = prev.length - 1;
            const updated = [...prev];
            updated[lastIndex].content = visibleContent;
            return updated;
          });

          if (autoScrollEnabled && scrollViewRef.current) {
            requestAnimationFrame(() => {
              scrollViewRef.current?.scrollToEnd({ animated: false });
            });
          }
            }
          );
        },
        userInput.length // Pass input token count estimate
      );

      // Extract the actual completion result
      const result: CompletionResult = inferenceResult;

      // Calculate performance metrics including operation data
      const totalTime = Date.now() - generationStartTime;
      const ttft = firstTokenTime ? (firstTokenTime - generationStartTime) / 1000 : 0;
      const actualTokensPerSecond = tokenCount > 0 ? tokenCount / (totalTime / 1000) : 0;

      // Log detailed performance data from native monitoring
      if (operationData) {
        console.log('🔍 Detailed Inference Performance:', {
          operation: operationData.name,
          duration: operationData.duration,
          memoryDelta: operationData.memoryDelta,
          cpuDelta: operationData.cpuDelta,
          tokensPerSecond: actualTokensPerSecond,
          ttft: ttft
        });
      }

      setTokensPerSecond((prev) => [
        ...prev,
        {
          tokensPerSecond: parseFloat(actualTokensPerSecond.toFixed(2)),
          ttft: parseFloat(ttft.toFixed(2)),
          ragTime: ragTiming ? parseFloat((ragTiming.queryTime / 1000).toFixed(2)) : 0,
          totalTokens: tokenCount
        }
      ]);

      // Update RAG stats
      setRagStats(EnhancedRAGService.getStats());

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      Alert.alert("Error During Inference", errorMessage);
    } finally {
      setIsLoading(false);
      setIsGenerating(false);
    }
  };

  const stopGeneration = async () => {
    try {
      await context.stopCompletion();
      setIsGenerating(false);
      setIsLoading(false);

      setConversation((prev) => {
        const lastMessage = prev[prev.length - 1];
        if (lastMessage.role === "assistant") {
          return [
            ...prev.slice(0, -1),
            {
              ...lastMessage,
              content: lastMessage.content + "\n\n*Generation stopped by user*",
            },
          ];
        }
        return prev;
      });
    } catch (error) {
      console.error("Error stopping completion:", error);
    }
  };

  const handleScroll = (event: any) => {
    const currentPosition = event.nativeEvent.contentOffset.y;
    const contentHeight = event.nativeEvent.contentSize.height;
    const scrollViewHeight = event.nativeEvent.layoutMeasurement.height;

    scrollPositionRef.current = currentPosition;
    contentHeightRef.current = contentHeight;

    const distanceFromBottom = contentHeight - scrollViewHeight - currentPosition;
    setAutoScrollEnabled(distanceFromBottom < 100);
  };

  const runDiagnostics = async () => {
    try {
      const diagnostics = await EnhancedRAGService.runDiagnostics();
      
      const failedTests = Object.values(diagnostics.results).filter(r => 
        typeof r === 'string' && r.includes('FAILED')
      ).length;
      const totalTests = Object.keys(diagnostics.results).length;
      
      Alert.alert(
        "RAG Diagnostics",
        `${totalTests - failedTests}/${totalTests} tests passed\n\nCheck console for detailed results.`,
        [{ text: "OK" }]
      );
    } catch (error) {
      Alert.alert("Diagnostics Error", (error as Error).message);
    }
  };

  const clearCaches = () => {
    RAGService.clearCaches();
    setRagStats(RAGService.getStats());
    Alert.alert("Cache Cleared", "All RAG caches have been cleared.");
  };

  const handleDocCountChange = (count: number | null) => {
    setCustomDocCount(count);
    if (count === null) {
      console.log(`📊 Document count reset to auto mode`);
    } else {
      console.log(`📊 Custom document count set to: ${count}`);
    }
  };

  const clearChat = () => {
    setConversation(INITIAL_CONVERSATION);
    setTokensPerSecond([]);
    console.log('🧹 Chat cleared');
  };

  const testTerm = async (term: string) => {
    try {
      const result = await RAGService.testTermEmbedding(term);
      console.log(`🔍 Term test result for "${term}":`, result);
      Alert.alert(
        "Term Test Results",
        `Platform: ${result.platform}\nTokenizer: ${result.tokenizerType}\nSearch Results: ${result.searchResultCount} docs\nEmbedding Dims: ${result.embeddingDimensions}`,
        [{ text: "OK" }]
      );
    } catch (error) {
      console.error('❌ Term test failed:', error);
      Alert.alert("Test Failed", (error as Error).message);
    }
  };

  const handleDocumentAdded = () => {
    // Refresh RAG stats when a document is added
    setRagStats(RAGService.getStats());
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <ScrollView
          style={styles.scrollView}
          ref={scrollViewRef}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          <Text style={styles.title}>{`SmartVA: 🔪\nEverything on Edge`}</Text>

          {/* Model Selection Page */}
          {currentPage === "modelSelection" && !isDownloading && (
            <View style={styles.card}>
              <Text style={styles.subtitle}>Choose a model format</Text>
              {MODEL_FORMATS.map((format) => (
                <TouchableOpacity
                  key={format.label}
                  style={[
                    styles.button,
                    selectedModelFormat === format.label && styles.selectedButton,
                    format.recommended && styles.recommendedButton,
                  ]}
                  onPress={() => handleFormatSelection(format.label)}
                >
                  <View style={styles.modelFormatContent}>
                    <Text style={[
                      styles.buttonText,
                      selectedModelFormat === format.label && styles.selectedButtonText
                    ]}>
                      {format.label}
                      {format.recommended && " ⭐"}
                    </Text>
                    <Text style={styles.modelDescription}>{format.description}</Text>
                  </View>
                </TouchableOpacity>
              ))}

              {selectedModelFormat && (
                <View>
                  <Text style={styles.subtitle}>Select a .gguf file</Text>
                  {isFetching && <ActivityIndicator size="small" color="#2563EB" />}
                  {availableGGUFs.map((file, index) => {
                    const isDownloaded = downloadedModels.includes(file);
                    return (
                      <TouchableOpacity
                        key={index}
                        style={[
                          styles.modelButton,
                          selectedGGUF === file && styles.selectedButton,
                          isDownloaded && styles.downloadedModelButton,
                        ]}
                        onPress={() =>
                          isDownloaded
                            ? (loadModel(file), setCurrentPage("conversation"), setSelectedGGUF(file))
                            : handleGGUFSelection(file)
                        }
                      >
                        <View style={styles.modelButtonContent}>
                          <View style={styles.modelStatusContainer}>
                            <View style={isDownloaded ? styles.downloadedIndicator : styles.notDownloadedIndicator}>
                              <Text style={isDownloaded ? styles.downloadedIcon : styles.notDownloadedIcon}>
                                {isDownloaded ? "▼" : "▽"}
                              </Text>
                            </View>
                            <Text style={[
                              styles.buttonTextGGUF,
                              selectedGGUF === file && styles.selectedButtonText,
                              isDownloaded && styles.downloadedText,
                            ]}>
                              {file.split("-").pop()}
                            </Text>
                          </View>
                          
                          {isDownloaded ? (
                            <View style={styles.loadModelIndicator}>
                              <Text style={styles.loadModelText}>TAP TO LOAD →</Text>
                            </View>
                          ) : (
                            <View style={styles.downloadIndicator}>
                              <Text style={styles.downloadText}>DOWNLOAD →</Text>
                            </View>
                          )}
                        </View>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              )}
            </View>
          )}

          {/* Conversation Page */}
          {currentPage === "conversation" && !isDownloading && (
            <View style={styles.chatWrapper}>
              <View style={styles.headerRow}>
                <Text style={styles.subtitle2}>Chatting with {selectedGGUF}</Text>
              </View>

              {/* RAG Status Bar */}
              <RAGStatusBar
                ragEnabled={ragEnabled}
                onToggleRAG={toggleRAG}
                ragStats={ragStats}
                docCount={customDocCount || getOptimalChunkCount(selectedGGUF)}
              />

              {/* Document Manager Button */}
              {ragEnabled && ragStats.isInitialized && (
                <View style={styles.documentManagerRow}>
                  <TouchableOpacity
                    style={styles.documentManagerButton}
                    onPress={() => setShowDocumentManager(true)}
                  >
                    <Icon name="plus-circle" size={16} color="#3B82F6" />
                    <Text style={styles.documentManagerButtonText}>
                      Add Documents ({ragStats.userDocuments || 0})
                    </Text>
                  </TouchableOpacity>
                </View>
              )}

              {/* Performance Monitoring */}
              <PerformanceMetricsDisplay style={{}} />
              <MemoryDeltaDisplay style={{}} />

              {/* Chat Container */}
              <View style={styles.chatContainer}>
                <Text style={styles.greetingText}>
                  🤖 Welcome! The model is ready to chat{ragEnabled && ragStats.isInitialized ? ' with RAG enhancement' : ''}. Ask away! 🎉
                </Text>

                {conversation.slice(1).map((msg, index) => (
                  <EnhancedMessageBubble
                    key={index}
                    message={msg}
                    index={index + 1}
                    tokensPerSecond={tokensPerSecond}
                    onToggleContext={toggleContext}
                    onToggleThought={toggleThought}
                    userBubbleStyle={styles.userBubble}
                    llamaBubbleStyle={styles.llamaBubble}
                  />
                ))}
              </View>

              {/* Debug Panel */}
              <DebugPanel
                ragStats={ragStats}
                onRunDiagnostics={runDiagnostics}
                onClearCaches={clearCaches}
                docCount={customDocCount || getOptimalChunkCount(selectedGGUF)}
                onDocCountChange={handleDocCountChange}
                selectedModel={selectedGGUF}
                onTestTerm={testTerm}
              />
            </View>
          )}

          {/* Download Progress */}
          {isDownloading && (
            <View style={styles.card}>
              <Text style={styles.subtitle}>Downloading: {selectedGGUF}</Text>
              <ProgressBar progress={progress} />
            </View>
          )}

          {/* RAG Loading */}
          {isLoadingRAG && (
            <View style={styles.ragLoadingCard}>
              <Text style={styles.subtitle}>Initializing RAG system...</Text>
              <ActivityIndicator size="large" color="#2563EB" />
            </View>
          )}
        </ScrollView>

        {/* Bottom Input Area */}
        <View style={styles.bottomContainer}>
          {currentPage === "conversation" && (
            <>
              <View style={styles.inputContainer}>
                <View style={styles.inputRow}>
                  <TextInput
                    style={styles.input}
                    placeholder="Type your message..."
                    placeholderTextColor="#94A3B8"
                    value={userInput}
                    onChangeText={setUserInput}
                    multiline
                  />
                  {isGenerating ? (
                    <TouchableOpacity
                      style={styles.stopButton}
                      onPress={stopGeneration}
                    >
                      <Text style={styles.buttonText}>□ Stop</Text>
                    </TouchableOpacity>
                  ) : (
                    <TouchableOpacity
                      style={styles.sendButton}
                      onPress={handleSendMessage}
                      disabled={isLoading}
                    >
                      <Text style={styles.buttonText}>
                        {isLoading ? "Sending..." : "Send"}
                      </Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
              <View style={styles.inputContainer}>
                <View style={styles.inputRow}>
                  <TouchableOpacity
                   style={styles.backButton}
                   onPress={handleBackToModelSelection}
                  >
                   <Text style={styles.backButtonText}>
                    ← Back to Model Selection
                   </Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                   style={styles.clearChatButton}
                   onPress={clearChat}
                  >
                   <Icon name="trash-o" size={20} color="#FFFFFF" />
                   <Text style={styles.clearChatButtonText}>Chat</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </>
          )}
        </View>

        {/* Document Manager Modal */}
        <DocumentManager
          visible={showDocumentManager}
          onClose={() => setShowDocumentManager(false)}
          onDocumentAdded={handleDocumentAdded}
        />
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollView: {
    paddingBottom: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: "700",
    color: "#1E293B",
    marginVertical: 24,
    textAlign: "center",
  },
  card: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    margin: 16,
    shadowColor: "#475569",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  subtitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#334155",
    marginBottom: 16,
    marginTop: 16,
  },
  subtitle2: {
    fontSize: 12,
    fontWeight: "600",
    marginBottom: 16,
    color: "#93C5FD",
  },
  button: {
    backgroundColor: "#93C5FD",
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginVertical: 6,
    shadowColor: "#93C5FD",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 2,
  },
  recommendedButton: {
    backgroundColor: "#3B82F6",
  },
  selectedButton: {
    backgroundColor: "#2563EB",
  },
  buttonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  selectedButtonText: {
    color: "#FFFFFF",
    fontWeight: "700",
  },
  modelFormatContent: {
    alignItems: "center",
  },
  modelDescription: {
    color: "#E0E7FF",
    fontSize: 12,
    marginTop: 4,
    textAlign: "center",
  },
  modelButton: {
    backgroundColor: "#EFF6FF",
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: "#BFDBFE",
    marginVertical: 6,
    shadowColor: "#3B82F6",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  downloadedModelButton: {
    backgroundColor: "#EFF6FF",
    borderColor: "#3B82F6",
    borderWidth: 1,
  },
  modelButtonContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  modelStatusContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  downloadedIndicator: {
    backgroundColor: "#DBEAFE",
    padding: 4,
    borderRadius: 6,
    marginRight: 8,
  },
  notDownloadedIndicator: {
    backgroundColor: "#F1F5F9",
    padding: 4,
    borderRadius: 6,
    marginRight: 8,
  },
  downloadedIcon: {
    color: "#3B82F6",
    fontSize: 14,
    fontWeight: "bold",
  },
  notDownloadedIcon: {
    color: "#94A3B8",
    fontSize: 14,
    fontWeight: "bold",
  },
  buttonTextGGUF: {
    color: "#1E40AF",
    fontSize: 14,
    fontWeight: "500",
  },
  downloadedText: {
    color: "#1E40AF",
  },
  loadModelIndicator: {
    backgroundColor: "#DBEAFE",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
    marginLeft: 8,
  },
  loadModelText: {
    color: "#3B82F6",
    fontSize: 8,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  downloadIndicator: {
    backgroundColor: "#DCF9E5",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 6,
    marginLeft: 8,
  },
  downloadText: {
    color: "#16A34A",
    fontSize: 8,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  chatWrapper: {
    flex: 1,
    padding: 16,
  },
  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  clearChatButton: {
    backgroundColor: '#EF4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    justifyContent: "center",
    alignSelf: "stretch",
    alignItems: 'center',
  },
  clearChatButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '600',
  },
  chatContainer: {
    flex: 1,
    backgroundColor: "#F8FAFC",
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
  },
  greetingText: {
    fontSize: 12,
    fontWeight: "500",
    textAlign: "center",
    marginVertical: 12,
    color: "#64748B",
  },
  userBubble: {
    alignSelf: "flex-end",
    backgroundColor: "#3B82F6",
  },
  llamaBubble: {
    alignSelf: "flex-start",
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#E2E8F0",
  },
  bottomContainer: {
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#E2E8F0",
    paddingBottom: Platform.OS === "ios" ? 20 : 10,
  },
  inputContainer: {
    padding: 16,
    backgroundColor: "#FFFFFF",
  },
  inputRow: {
    flexDirection: "row",
    gap: 12,
  },
  input: {
    flex: 1,
    backgroundColor: "#FFFFFF",
    borderWidth: 1,
    borderColor: "#E2E8F0",
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    color: "#334155",
    minHeight: 50,
    maxHeight: 120,
  },
  sendButton: {
    backgroundColor: "#3B82F6",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    shadowColor: "#3B82F6",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 2,
    alignSelf: "stretch",
    justifyContent: "center",
  },
  stopButton: {
    backgroundColor: "#FF3B30",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    alignSelf: "stretch",
    justifyContent: "center",
  },
  backButton: {
    backgroundColor: "#3B82F6",
    marginHorizontal: 16,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 12,
    alignItems: "center",
  },
  backButtonText: {
    color: "#FFFFFF",
    fontSize: 16,
    fontWeight: "600",
  },
  ragLoadingCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    margin: 16,
    shadowColor: "#475569",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    alignItems: "center",
  },
  documentManagerRow: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  documentManagerButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EFF6FF',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#BFDBFE',
  },
  documentManagerButtonText: {
    color: '#3B82F6',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
});

export default App;
    