#!/usr/bin/env python3
"""
Test script for the custom Rust hnsw_lib library using Python ctypes.

This script tests the Rust HNSW library by:
1. Loading the compiled shared library
2. Setting up proper C function signatures
3. Testing the hnsw_search function with real data
4. Using the new chunked metadata strategy for document loading

Updated to work with the chunked metadata strategy from embed_docs.py
"""

import os
import sys
import ctypes
import numpy as np
import time
import json
import math

# Import required packages for embedding
try:
    import onnxruntime as ort
    from transformers import AutoTokenizer
    print("✅ Successfully imported embedding dependencies")
except ImportError as e:
    print(f"❌ Failed to import embedding dependencies: {e}")
    sys.exit(1)

# Embedding model configuration
MODEL_PATH = "all-MiniLM-L6-v2.quant.onnx"
TOKENIZER_NAME = "sentence-transformers/all-MiniLM-L6-v2"

# Constants from the Rust library
OK = 0
ERR_SEARCH = 1

# Chunked metadata configuration (should match embed_docs.py)
DOCS_PER_METADATA_FILE = 1000

class ChunkedMetadataLoader:
    """Handles loading documents from chunked metadata files"""
    
    def __init__(self, metadata_dir="hnsw_rag/metadata_chunks"):
        self.metadata_dir = metadata_dir
        self.metadata_index = None
        self.chunk_cache = {}  # Simple cache for loaded chunks
        self.load_metadata_index()
    
    def load_metadata_index(self):
        """Load the metadata index file"""
        index_path = os.path.join(self.metadata_dir, "metadata_index.json")
        
        if not os.path.exists(index_path):
            print(f"❌ Metadata index not found: {index_path}")
            print("Please run embed_docs.py to generate the chunked metadata")
            return False
        
        try:
            with open(index_path, 'r', encoding='utf-8') as f:
                self.metadata_index = json.load(f)
            
            print(f"✅ Loaded metadata index:")
            print(f"  - Total documents: {self.metadata_index['total_documents']}")
            print(f"  - Total files: {self.metadata_index['total_files']}")
            print(f"  - Docs per file: {self.metadata_index['docs_per_file']}")
            return True
            
        except Exception as e:
            print(f"❌ Error loading metadata index: {e}")
            return False
    
    def load_metadata_chunk(self, chunk_index):
        """Load a specific metadata chunk file with caching"""
        if chunk_index in self.chunk_cache:
            return self.chunk_cache[chunk_index]
        
        if not self.metadata_index or chunk_index >= self.metadata_index['total_files']:
            print(f"❌ Invalid chunk index: {chunk_index}")
            return None
        
        chunk_info = self.metadata_index['files'][chunk_index]
        filename = chunk_info['filename']
        chunk_path = os.path.join(self.metadata_dir, filename)
        
        if not os.path.exists(chunk_path):
            print(f"❌ Chunk file not found: {chunk_path}")
            return None
        
        try:
            with open(chunk_path, 'r', encoding='utf-8') as f:
                chunk_data = json.load(f)
            
            # Cache the loaded chunk (limit cache size)
            if len(self.chunk_cache) >= 10:  # Keep max 10 chunks in memory
                # Remove oldest entry
                oldest_key = next(iter(self.chunk_cache))
                del self.chunk_cache[oldest_key]
            
            self.chunk_cache[chunk_index] = chunk_data
            print(f"📄 Loaded chunk {chunk_index}: {len(chunk_data)} documents")
            return chunk_data
            
        except Exception as e:
            print(f"❌ Error loading chunk {chunk_index}: {e}")
            return None
    
    def get_document_by_index(self, doc_index):
        """Get a document by its index using chunked metadata"""
        if not self.metadata_index:
            print("❌ Metadata index not loaded")
            return None
        
        if doc_index < 0 or doc_index >= self.metadata_index['total_documents']:
            print(f"❌ Document index {doc_index} out of range [0, {self.metadata_index['total_documents']-1}]")
            return None
        
        # Calculate which chunk contains this document
        chunk_index = doc_index // self.metadata_index['docs_per_file']
        position_in_chunk = doc_index % self.metadata_index['docs_per_file']
        
        print(f"🎯 Document {doc_index} → chunk {chunk_index}, position {position_in_chunk}")
        
        # Load the chunk
        chunk_data = self.load_metadata_chunk(chunk_index)
        if not chunk_data:
            return None
        
        # Get the document from the chunk
        if position_in_chunk >= len(chunk_data):
            print(f"❌ Position {position_in_chunk} out of range in chunk {chunk_index} (length: {len(chunk_data)})")
            return None
        
        document = chunk_data[position_in_chunk]
        
        # Validate document structure
        if not document or 'id' not in document or 'content' not in document:
            print(f"❌ Invalid document structure at index {doc_index}")
            return None
        
        return document
    
    def get_documents_by_indices(self, indices):
        """Get multiple documents by their indices efficiently"""
        if not self.metadata_index:
            return []
        
        documents = []
        
        # Group indices by chunk to minimize file I/O
        chunk_groups = {}
        for doc_index in indices:
            if 0 <= doc_index < self.metadata_index['total_documents']:
                chunk_index = doc_index // self.metadata_index['docs_per_file']
                position = doc_index % self.metadata_index['docs_per_file']
                
                if chunk_index not in chunk_groups:
                    chunk_groups[chunk_index] = []
                chunk_groups[chunk_index].append((doc_index, position))
        
        # Load each required chunk and extract documents
        for chunk_index, doc_positions in chunk_groups.items():
            chunk_data = self.load_metadata_chunk(chunk_index)
            if chunk_data:
                for doc_index, position in doc_positions:
                    if position < len(chunk_data):
                        doc = chunk_data[position]
                        documents.append({
                            "id": doc.get("id", f"doc_{doc_index}"),
                            "content": doc.get("content", "")[:300] + "..." if len(doc.get("content", "")) > 300 else doc.get("content", ""),
                            "metadata": doc.get("metadata", {}),
                            "index": doc_index
                        })
                    else:
                        documents.append({
                            "id": f"doc_{doc_index}",
                            "content": "Document position out of range in chunk",
                            "metadata": {},
                            "index": doc_index
                        })
            else:
                # Add placeholder for failed chunk
                for doc_index, _ in doc_positions:
                    documents.append({
                        "id": f"doc_{doc_index}",
                        "content": "Failed to load document chunk",
                        "metadata": {},
                        "index": doc_index
                    })
        
        return documents

def load_model():
    """Load the ONNX embedding model"""
    print(f"Loading ONNX model from {MODEL_PATH}...")
    try:
        session = ort.InferenceSession(MODEL_PATH)
        print("✅ ONNX session created successfully!")
        return session
    except Exception as e:
        print(f"❌ Failed to create ONNX session: {e}")
        return None

def tokenize_text(text, max_length=512):
    """Tokenize text using the transformer tokenizer"""
    tokenizer = AutoTokenizer.from_pretrained(TOKENIZER_NAME)
    tokens = tokenizer(
        text,
        padding="max_length",
        truncation=True,
        max_length=max_length,
        return_tensors="np"
    )
    return tokens

def embed_text(session, text):
    """Generate embedding for text using the ONNX model"""
    try:
        # Tokenize the text
        tokens = tokenize_text(text)

        # Create ONNX feeds
        feeds = {
            'input_ids': tokens['input_ids'],
            'attention_mask': tokens['attention_mask']
        }

        # Run inference
        results = session.run(None, feeds)
        embedding = results[0]

        # Apply mean pooling if needed (for 3D output)
        if len(embedding.shape) == 3:
            attention_mask = tokens['attention_mask']
            mask_expanded = np.expand_dims(attention_mask, axis=2)
            sum_embeddings = np.sum(embedding * mask_expanded, axis=1)
            sum_mask = np.sum(attention_mask, axis=1, keepdims=True)
            embedding = sum_embeddings / np.maximum(sum_mask, 1e-9)

        # Normalize the embedding
        norm = np.linalg.norm(embedding, axis=1, keepdims=True)
        normalized_embedding = embedding / np.maximum(norm, 1e-9)

        return normalized_embedding[0]

    except Exception as e:
        print(f"Error embedding text: {e}")
        return None

class RustHnswLib:
    """Python wrapper for the Rust HNSW library"""
    
    def __init__(self, lib_path=None):
        if lib_path is None:
            # Default path to the compiled library (from project root)
            lib_path = "../rust/hnsw_lib/target/release/libhnsw_lib.dylib"
        
        if not os.path.exists(lib_path):
            raise FileNotFoundError(f"Rust library not found at {lib_path}")
        
        # Load the shared library
        self.lib = ctypes.CDLL(lib_path)
        
        # Define the function signature for hnsw_search
        # int hnsw_search(float* query_ptr, size_t dim, size_t k, int* out_ptr, 
        #                 const char* dir_ptr, const char* base_ptr)
        self.lib.hnsw_search.argtypes = [
            ctypes.POINTER(ctypes.c_float),  # query_ptr
            ctypes.c_size_t,                 # dim
            ctypes.c_size_t,                 # k
            ctypes.POINTER(ctypes.c_int),    # out_ptr
            ctypes.c_char_p,                 # dir_ptr
            ctypes.c_char_p                  # base_ptr
        ]
        self.lib.hnsw_search.restype = ctypes.c_int
        
        print(f"✅ Successfully loaded Rust HNSW library from {lib_path}")
    
    def search(self, query_vector, k=5, index_dir=None, base_filename="documents_hnsw_rs"):
        """
        Search the HNSW index using the Rust implementation

        Args:
            query_vector: numpy array of query embedding
            k: number of nearest neighbors to return
            index_dir: directory containing the HNSW index files
            base_filename: base filename for the index files

        Returns:
            dict with 'neighbors' and 'distances' keys, or None if error
        """
        if index_dir is None:
            index_dir = os.path.abspath("hnsw_rag")  # Use absolute path
        
        # Ensure query_vector is a numpy array of float32
        if not isinstance(query_vector, np.ndarray):
            query_vector = np.array(query_vector, dtype=np.float32)
        elif query_vector.dtype != np.float32:
            query_vector = query_vector.astype(np.float32)
        
        # Ensure it's 1D
        if query_vector.ndim > 1:
            query_vector = query_vector.flatten()
        
        dim = len(query_vector)
        
        # Prepare output array for neighbor IDs
        neighbors = np.zeros(k, dtype=np.int32)
        
        # Convert strings to bytes for C
        dir_bytes = index_dir.encode('utf-8')
        base_bytes = base_filename.encode('utf-8')
        
        # Create ctypes pointers
        query_ptr = query_vector.ctypes.data_as(ctypes.POINTER(ctypes.c_float))
        out_ptr = neighbors.ctypes.data_as(ctypes.POINTER(ctypes.c_int))
        
        print(f"🔍 Calling Rust hnsw_search with:")
        print(f"  - Query dimension: {dim}")
        print(f"  - K: {k}")
        print(f"  - Index directory: {index_dir}")
        print(f"  - Base filename: {base_filename}")
        
        # Call the Rust function
        start_time = time.time()
        result = self.lib.hnsw_search(
            query_ptr,
            dim,
            k,
            out_ptr,
            dir_bytes,
            base_bytes
        )
        search_time = time.time() - start_time
        
        print(f"  - Search completed in {search_time:.4f} seconds")
        print(f"  - Return code: {result}")
        
        if result == OK:
            print(f"✅ Rust HNSW search successful!")
            print(f"  - Found neighbors: {neighbors.tolist()}")
            
            # Note: The Rust implementation doesn't return distances,
            # so we'll return None for distances
            return {
                "neighbors": neighbors.tolist(),
                "distances": [None] * k,
                "search_time": search_time
            }
        else:
            print(f"❌ Rust HNSW search failed with code {result}")
            return None

def test_rust_library():
    """Test the Rust HNSW library with real embeddings and chunked metadata"""
    print("🧪 Testing Rust HNSW Library with Chunked Metadata")
    print("=" * 60)
    
    # Initialize the chunked metadata loader
    print("\n📥 Initializing chunked metadata loader...")
    metadata_loader = ChunkedMetadataLoader()
    if not metadata_loader.metadata_index:
        print("❌ Failed to load metadata index")
        return False
    
    # Initialize the Rust library
    try:
        rust_lib = RustHnswLib()
    except Exception as e:
        print(f"❌ Failed to initialize Rust library: {e}")
        return False
    
    # Load the embedding model
    print("\n📥 Loading embedding model...")
    session = load_model()
    if not session:
        print("❌ Failed to load embedding model")
        return False
    
    # Check if HNSW index exists
    rag_index_dir = "hnsw_rag"
    if not os.path.exists(rag_index_dir):
        print(f"❌ HNSW index directory not found: {rag_index_dir}")
        print("Please run the embedding script first to create the index")
        return False
    
    # Test queries
    test_queries = [
        "What is a cryptoasset?",
        "Financial regulations",
        "Blockchain technology",
        "Digital currency"
    ]
    
    print(f"\n🔍 Testing with {len(test_queries)} queries...")
    
    for i, query in enumerate(test_queries):
        print(f"\n--- Test {i+1}: '{query}' ---")
        
        # Generate embedding for the query
        print("🔄 Generating query embedding...")
        query_embedding = embed_text(session, query)
        if query_embedding is None:
            print("❌ Failed to generate embedding")
            continue
        
        print(f"✅ Generated embedding with dimension {query_embedding.shape[0]}")
        
        # Test Rust implementation
        print("\n🦀 Testing Rust implementation...")
        rust_results = rust_lib.search(query_embedding, k=5)

        if rust_results:
            neighbor_ids = rust_results["neighbors"]
            search_time = rust_results["search_time"]

            print(f"✅ Search completed in {search_time:.4f}s")
            print(f"📄 Retrieved {len(neighbor_ids)} documents:")

            # Get the actual documents using chunked metadata
            print("📖 Loading documents from chunked metadata...")
            documents = metadata_loader.get_documents_by_indices(neighbor_ids)

            for i, doc in enumerate(documents):
                print(f"\n📄 Document {i+1} (Index: {doc['index']}):")
                print(f"   ID: {doc['id']}")

                # Show metadata if available
                metadata = doc['metadata']
                if metadata:
                    if 'source' in metadata:
                        print(f"   Source: {metadata['source']}")
                    if 'type' in metadata:
                        print(f"   Type: {metadata['type']}")
                    if 'chunk_index' in metadata and 'total_chunks' in metadata:
                        print(f"   Chunk: {metadata['chunk_index'] + 1}/{metadata['total_chunks']}")
                    if 'url' in metadata:
                        print(f"   URL: {metadata['url']}")

                # Show content preview
                content = doc['content'].strip()
                if content:
                    print(f"   Content: {content}")
                else:
                    print(f"   Content: [No content available]")

                # Check if the content seems relevant to the query
                query_words = query.lower().split()
                content_lower = content.lower()
                matching_words = [word for word in query_words if len(word) > 3 and word in content_lower]

                if matching_words:
                    print(f"   🎯 Relevance: Contains words: {', '.join(matching_words)}")
                else:
                    print(f"   🤔 Relevance: No direct word matches (semantic similarity)")
        else:
            print("❌ Rust search failed")
        
        print("-" * 50)
    
    # Test document loading efficiency
    print(f"\n🧪 Testing chunked metadata efficiency...")
    print(f"Cache status: {len(metadata_loader.chunk_cache)} chunks cached")
    
    # Test loading a specific document
    test_doc_index = min(100, metadata_loader.metadata_index['total_documents'] - 1)
    print(f"Testing direct document access for index {test_doc_index}...")
    
    start_time = time.time()
    test_doc = metadata_loader.get_document_by_index(test_doc_index)
    load_time = time.time() - start_time
    
    if test_doc:
        print(f"✅ Loaded document in {load_time:.4f}s")
        print(f"   ID: {test_doc['id']}")
        print(f"   Source: {test_doc['metadata'].get('source', 'Unknown')}")
        print(f"   Content length: {len(test_doc['content'])} chars")
    else:
        print("❌ Failed to load test document")
    
    return True

def test_error_handling():
    """Test error handling in the Rust library"""
    print("\n🧪 Testing Error Handling")
    print("=" * 30)
    
    try:
        rust_lib = RustHnswLib()
    except Exception as e:
        print(f"❌ Failed to initialize Rust library: {e}")
        return False
    
    # Test with invalid directory
    print("🔍 Testing with invalid directory...")
    fake_embedding = np.random.rand(384).astype(np.float32)
    result = rust_lib.search(fake_embedding, k=5, index_dir="/nonexistent/path")
    
    if result is None:
        print("✅ Correctly handled invalid directory")
    else:
        print("⚠️ Unexpected success with invalid directory")
    
    # Test with invalid base filename
    print("🔍 Testing with invalid base filename...")
    result = rust_lib.search(fake_embedding, k=5, base_filename="nonexistent_file")
    
    if result is None:
        print("✅ Correctly handled invalid base filename")
    else:
        print("⚠️ Unexpected success with invalid base filename")
    
    return True

def test_chunked_metadata_performance():
    """Test the performance of chunked metadata loading"""
    print("\n🧪 Testing Chunked Metadata Performance")
    print("=" * 40)
    
    metadata_loader = ChunkedMetadataLoader()
    if not metadata_loader.metadata_index:
        print("❌ Cannot test without metadata index")
        return False
    
    total_docs = metadata_loader.metadata_index['total_documents']
    print(f"📊 Testing with {total_docs} total documents")
    
    # Test loading documents from different chunks
    test_indices = [
        0,  # First document
        total_docs // 4,  # 25% through
        total_docs // 2,  # Middle
        total_docs * 3 // 4,  # 75% through
        total_docs - 1  # Last document
    ]
    
    print("🔄 Testing document loading from different chunks...")
    
    for doc_index in test_indices:
        start_time = time.time()
        doc = metadata_loader.get_document_by_index(doc_index)
        load_time = time.time() - start_time
        
        if doc:
            print(f"✅ Doc {doc_index}: {load_time:.4f}s - {doc['metadata'].get('source', 'Unknown')}")
        else:
            print(f"❌ Doc {doc_index}: Failed to load")
    
    # Test batch loading
    print("\n🔄 Testing batch document loading...")
    batch_indices = test_indices
    
    start_time = time.time()
    batch_docs = metadata_loader.get_documents_by_indices(batch_indices)
    batch_time = time.time() - start_time
    
    print(f"✅ Batch loaded {len(batch_docs)}/{len(batch_indices)} docs in {batch_time:.4f}s")
    print(f"📈 Average: {batch_time/len(batch_indices):.4f}s per document")
    print(f"💾 Cache efficiency: {len(metadata_loader.chunk_cache)} chunks cached")
    
    return True

if __name__ == "__main__":
    print("🚀 Starting Rust HNSW Library Tests with Chunked Metadata")
    print("=" * 70)
    
    # Test the main functionality
    success = test_rust_library()
    
    if success:
        # Test chunked metadata performance
        test_chunked_metadata_performance()
        
        # Test error handling
        test_error_handling()
        
        print("\n🎉 All tests completed!")
        print("\n💡 Next steps:")
        print("  1. If tests pass, your Rust library is working correctly")
        print("  2. The chunked metadata system is working efficiently")
        print("  3. You can now integrate it into your iOS/Android apps")
        print("  4. Consider performance optimizations if needed")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")
        print("\n🔧 Troubleshooting:")
        print("  1. Make sure the HNSW index exists (run embed_docs.py)")
        print("  2. Check that the Rust library compiled correctly")
        print("  3. Verify the embedding model is available")
        print("  4. Ensure chunked metadata files are generated correctly")