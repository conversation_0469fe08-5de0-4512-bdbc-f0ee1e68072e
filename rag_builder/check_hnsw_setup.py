#!/usr/bin/env python3
"""
Check if the HNSW setup is ready for testing the Rust library.
This script verifies that all necessary components are in place.
"""

import os
import sys
from pathlib import Path

def check_rust_library():
    """Check if the Rust library is compiled"""
    lib_path = "rust/hnsw_lib/target/release/libhnsw_lib.dylib"
    
    print("🦀 Checking Rust library...")
    if os.path.exists(lib_path):
        print(f"✅ Rust library found: {lib_path}")
        return True
    else:
        print(f"❌ Rust library not found: {lib_path}")
        print("💡 To build it, run: cd rust/hnsw_lib && cargo build --release")
        return False

def check_embedding_model():
    """Check if the embedding model is available"""
    model_path = "rag_builder/all-MiniLM-L6-v2.quant.onnx"
    
    print("\n🤖 Checking embedding model...")
    if os.path.exists(model_path):
        print(f"✅ Embedding model found: {model_path}")
        return True
    else:
        print(f"❌ Embedding model not found: {model_path}")
        print("💡 You may need to download or build the ONNX model")
        return False

def check_hnsw_index():
    """Check if the HNSW index files exist"""
    index_dir = "rag_builder/hnsw_rag"
    
    print(f"\n📚 Checking HNSW index in {index_dir}...")
    
    if not os.path.exists(index_dir):
        print(f"❌ Index directory not found: {index_dir}")
        print("💡 Run the embedding script to create the index")
        return False
    
    # Check for required files
    required_files = [
        "documents_hnsw_rs.hnsw.data",
        "documents_hnsw_rs.hnsw.graph", 
        "documents_metadata.json"
    ]
    
    missing_files = []
    for file in required_files:
        file_path = os.path.join(index_dir, file)
        if os.path.exists(file_path):
            print(f"✅ Found: {file}")
        else:
            print(f"❌ Missing: {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n❌ Missing {len(missing_files)} required files")
        print("💡 Run the embedding script to create the complete index")
        return False
    else:
        print("✅ All required index files found")
        return True

def check_python_dependencies():
    """Check if required Python packages are installed"""
    print("\n🐍 Checking Python dependencies...")
    
    required_packages = [
        "numpy",
        "onnxruntime", 
        "transformers",
        "ctypes"  # This is built-in, but let's check
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            if package == "ctypes":
                import ctypes
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("💡 Install with: pip install " + " ".join(missing_packages))
        return False
    else:
        print("✅ All required packages available")
        return True

def check_hnsw_builder():
    """Check if the HNSW builder is compiled"""
    builder_path = "rust/hnsw_builder/target/release/hnsw_builder"
    
    print(f"\n🔨 Checking HNSW builder...")
    if os.path.exists(builder_path):
        print(f"✅ HNSW builder found: {builder_path}")
        return True
    else:
        print(f"❌ HNSW builder not found: {builder_path}")
        print("💡 To build it, run: cd rust/hnsw_builder && cargo build --release")
        return False

def provide_setup_instructions():
    """Provide step-by-step setup instructions"""
    print("\n" + "="*60)
    print("📋 SETUP INSTRUCTIONS")
    print("="*60)
    
    print("\n1. 🦀 Build the Rust libraries:")
    print("   cd rust/hnsw_lib && cargo build --release")
    print("   cd ../hnsw_builder && cargo build --release")
    
    print("\n2. 🐍 Install Python dependencies:")
    print("   cd rag_builder")
    print("   pip install -r requirements.txt")
    
    print("\n3. 📚 Create the HNSW index:")
    print("   cd rag_builder")
    print("   python embed_docs.py")
    print("   (This will download documents and create the index)")
    
    print("\n4. 🧪 Run the tests:")
    print("   python test_rust_hnsw_lib.py")
    
    print("\n" + "="*60)

def main():
    """Main function to run all checks"""
    print("🔍 HNSW Setup Checker")
    print("="*40)
    
    checks = [
        ("Rust Library", check_rust_library),
        ("Python Dependencies", check_python_dependencies),
        ("Embedding Model", check_embedding_model),
        ("HNSW Index", check_hnsw_index),
        ("HNSW Builder", check_hnsw_builder)
    ]
    
    results = {}
    for name, check_func in checks:
        results[name] = check_func()
    
    # Summary
    print("\n" + "="*40)
    print("📊 SUMMARY")
    print("="*40)
    
    passed = sum(results.values())
    total = len(results)
    
    for name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{name:20} {status}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All checks passed! You're ready to test the Rust library.")
        print("Run: python test_rust_hnsw_lib.py")
    else:
        print(f"\n⚠️ {total - passed} checks failed. Setup needed.")
        provide_setup_instructions()

if __name__ == "__main__":
    main()
