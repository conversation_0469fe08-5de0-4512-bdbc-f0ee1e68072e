import os
import json
import onnxruntime as ort
from transformers import AutoTokenizer
import numpy as np
import subprocess
import pandas as pd
import PyPDF2
import glob
import pickle
import time
import csv
import shutil
import re
import math

# === Load tokenizer & ONNX model ===
model_path = "all-MiniLM-L6-v2.quant.onnx"
tokenizer = AutoTokenizer.from_pretrained("sentence-transformers/all-MiniLM-L6-v2")

session = ort.InferenceSession(model_path)
input_name = session.get_inputs()[0].name
attention_name = session.get_inputs()[1].name
output_name = session.get_outputs()[0].name

# Chunking parameters
CHUNK_SIZE = 1000  # Characters per chunk
CHUNK_OVERLAP = 200  # Overlap between chunks

# Metadata file parameters
DOCS_PER_METADATA_FILE = 1000  # Documents per metadata file
MAX_METADATA_FILE_SIZE_MB = 5  # Target max size per metadata file


def chunk_text(text, chunk_size=CHUNK_SIZE, overlap=CHUNK_OVERLAP):
    """
    Split text into overlapping chunks.
    
    Args:
        text: Input text to chunk
        chunk_size: Maximum characters per chunk
        overlap: Characters to overlap between chunks
    
    Returns:
        List of text chunks
    """
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    
    while start < len(text):
        # Calculate end position
        end = start + chunk_size
        
        # If this isn't the last chunk, try to break at sentence boundary
        if end < len(text):
            # Look for sentence endings within the last 200 characters
            search_start = max(start + chunk_size - 200, start)
            sentence_end = max(
                text.rfind('.', search_start, end),
                text.rfind('!', search_start, end),
                text.rfind('?', search_start, end)
            )
            
            # If we found a sentence boundary, use it
            if sentence_end > search_start:
                end = sentence_end + 1
            # Otherwise, try to break at word boundary
            else:
                last_space = text.rfind(' ', start, end)
                if last_space > start:
                    end = last_space
        
        chunk = text[start:end].strip()
        if chunk:  # Only add non-empty chunks
            chunks.append(chunk)
        
        # Move start position with overlap
        start = end - overlap
        
        # Prevent infinite loop
        if start >= end:
            start = end
    
    return chunks


def save_metadata_chunks(documents, output_dir):
    """
    Save documents metadata in multiple smaller files for efficient loading.
    
    Args:
        documents: List of document dictionaries
        output_dir: Output directory path
    
    Returns:
        Dictionary with metadata file information
    """
    # Create metadata directory
    metadata_dir = os.path.join(output_dir, "metadata_chunks")
    os.makedirs(metadata_dir, exist_ok=True)
    
    # Calculate how many files we need
    total_docs = len(documents)
    files_needed = math.ceil(total_docs / DOCS_PER_METADATA_FILE)
    
    print(f"📁 Splitting {total_docs} documents into {files_needed} metadata files")
    
    file_info = {
        "total_documents": total_docs,
        "docs_per_file": DOCS_PER_METADATA_FILE,
        "total_files": files_needed,
        "files": []
    }
    
    for file_idx in range(files_needed):
        start_idx = file_idx * DOCS_PER_METADATA_FILE
        end_idx = min(start_idx + DOCS_PER_METADATA_FILE, total_docs)
        
        file_docs = documents[start_idx:end_idx]
        filename = f"metadata_{file_idx:04d}.json"
        filepath = os.path.join(metadata_dir, filename)
        
        # Save this chunk of documents
        with open(filepath, "w", encoding="utf-8") as f:
            json.dump(file_docs, f, ensure_ascii=False, indent=2)
        
        # Get file size
        file_size = os.path.getsize(filepath)
        file_size_mb = file_size / (1024 * 1024)
        
        file_info["files"].append({
            "filename": filename,
            "start_index": start_idx,
            "end_index": end_idx - 1,
            "document_count": len(file_docs),
            "size_mb": round(file_size_mb, 2)
        })
        
        print(f"  📄 {filename}: docs {start_idx}-{end_idx-1} ({len(file_docs)} docs, {file_size_mb:.2f}MB)")
    
    # Save the file index
    index_path = os.path.join(metadata_dir, "metadata_index.json")
    with open(index_path, "w", encoding="utf-8") as f:
        json.dump(file_info, f, ensure_ascii=False, indent=2)
    
    print(f"💾 Metadata index saved to {index_path}")
    return file_info


def embed(texts):
    """Create embeddings for the given text using the ONNX model."""
    # Ensure texts is always a list
    if not isinstance(texts, list):
        texts = [texts]

    inputs = tokenizer(texts, return_tensors="np", padding=True, truncation=True)
    ort_inputs = {
        input_name: inputs["input_ids"],
        attention_name: inputs["attention_mask"]
    }
    ort_outs = session.run([output_name], ort_inputs)
    embeddings = ort_outs[0]

    # Normalize embeddings for cosine similarity
    norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
    # Avoid division by zero
    norms = np.maximum(norms, 1e-9)
    normalized = embeddings / norms

    # Always return the same format - vector for single input
    if len(texts) == 1:
        # For a single text, mean pool across tokens to get a single vector
        # This ensures we always return a 1D vector
        return np.mean(normalized, axis=0)
    else:
        # For multiple texts, return mean pooled vectors for each text
        return np.array([np.mean(normalized[i], axis=0) for i in range(len(texts))])


def read_pdf(file_path, password=None):
    """Extract text from a PDF file with multiple fallback options for corrupted files."""
    # Try PyPDF2 first (most common)
    try:
        with open(file_path, 'rb') as file:
            try:
                pdf_reader = PyPDF2.PdfReader(file)

                # Handle encryption
                if pdf_reader.is_encrypted:
                    if password:
                        try:
                            pdf_reader.decrypt(password)
                        except:
                            print(f"Warning: Provided password incorrect for {file_path}")
                            return f"[Encrypted PDF: {os.path.basename(file_path)}]"

                    # Try empty password
                    if pdf_reader.is_encrypted:
                        try:
                            pdf_reader.decrypt("")
                        except:
                            print(f"Warning: Cannot decrypt PDF {file_path} - encryption requires password")
                            return f"[Encrypted PDF: {os.path.basename(file_path)}]"

                # Extract text from pages
                text = ""
                for page_num in range(len(pdf_reader.pages)):
                    try:
                        page_text = pdf_reader.pages[page_num].extract_text()
                        if page_text:
                            text += page_text + " "
                    except Exception as page_e:
                        print(f"Warning: Error extracting text from page {page_num} in {file_path}: {page_e}")

                if text.strip():
                    return text.strip()

            except Exception as e:
                print(f"PyPDF2 couldn't process {file_path}: {e}")
    except Exception as file_e:
        print(f"Error opening file {file_path}: {file_e}")

    # Try pdfplumber (often works when PyPDF2 fails)
    try:
        import pdfplumber
        with pdfplumber.open(file_path, password=password) as pdf:
            text = ""
            for page in pdf.pages:
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + " "
                except Exception as page_e:
                    print(f"pdfplumber error on page: {page_e}")

            if text.strip():
                print(f"Successfully extracted text using pdfplumber from {file_path}")
                return text.strip()
    except ImportError:
        print("pdfplumber not installed. Try: pip install pdfplumber")
    except Exception as e:
        print(f"pdfplumber couldn't process {file_path}: {e}")

    print(f"Failed to extract text from {file_path}")
    return f"[Error: Could not extract text from PDF: {os.path.basename(file_path)}]"


def read_csv(file_path):
    """Extract text content from a CSV file."""
    try:
        df = pd.read_csv(file_path)
        return df.to_string(index=False)
    except Exception as e:
        print(f"Error reading CSV {file_path}: {e}")
        return ""


def read_excel(file_path):
    """Extract text content from an Excel file."""
    try:
        df = pd.read_excel(file_path)
        return df.to_string(index=False)
    except Exception as e:
        print(f"Error reading Excel {file_path}: {e}")
        return ""


def read_json(file_path):
    """Extract text content from a JSON file with the specified structure."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        documents = []
        for item in data:
            if "url" in item and item["url"]:
                doc_parts = [f"URL: {item['url']}"]
                if "markdown" in item and item["markdown"]:
                    doc_parts.append(f"Content: {item['markdown']}")
                else:
                    doc_parts.append("Content: [No markdown content]")
                documents.append("\n".join(doc_parts))

        return documents
    except Exception as e:
        print(f"Error reading JSON {file_path}: {e}")
        return []


def read_text(file_path):
    """Read a text file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"Error reading text file {file_path}: {e}")
        return ""


def process_directory(directory_path):
    """Process all supported files in a directory and return a list of chunked documents."""
    documents = []
    doc_id = 0

    # Find all files with supported extensions
    for extension in ['*.pdf', '*.csv', '*.xlsx', '*.xls', '*.json', '*.txt']:
        file_paths = glob.glob(os.path.join(directory_path, extension))

        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            ext = os.path.splitext(file_name)[1].lower()

            print(f"Processing {file_path}...")

            content = None
            file_type = None

            if ext == '.pdf':
                content = read_pdf(file_path)
                file_type = "pdf"
            elif ext == '.csv':
                content = read_csv(file_path)
                file_type = "csv"
            elif ext in ['.xlsx', '.xls']:
                content = read_excel(file_path)
                file_type = "excel"
            elif ext == '.json':
                json_docs = read_json(file_path)
                for i, json_content in enumerate(json_docs):
                    chunks = chunk_text(json_content)
                    print(f"  Split JSON document {i} into {len(chunks)} chunks")
                    
                    for chunk_idx, chunk in enumerate(chunks):
                        documents.append({
                            "id": f"doc_{doc_id}",
                            "content": chunk,
                            "metadata": {
                                "source": file_name,
                                "type": "json",
                                "original_index": i,
                                "chunk_index": chunk_idx,
                                "total_chunks": len(chunks)
                            }
                        })
                        doc_id += 1
                continue
            elif ext == '.txt':
                content = read_text(file_path)
                file_type = "text"

            # Process non-JSON files
            if content and file_type:
                chunks = chunk_text(content)
                print(f"  Split {file_name} into {len(chunks)} chunks")
                
                for chunk_idx, chunk in enumerate(chunks):
                    documents.append({
                        "id": f"doc_{doc_id}",
                        "content": chunk,
                        "metadata": {
                            "source": file_name,
                            "type": file_type,
                            "chunk_index": chunk_idx,
                            "total_chunks": len(chunks)
                        }
                    })
                    doc_id += 1

    return documents


def copy_assets_to_mobile(output_dir):
    """Copy necessary files to mobile asset directories."""
    # Define source files
    data_file = os.path.join(output_dir, "documents_hnsw_rs.hnsw.data")
    graph_file = os.path.join(output_dir, "documents_hnsw_rs.hnsw.graph")
    metadata_index = os.path.join(output_dir, "metadata_chunks", "metadata_index.json")
    
    # Define destination directories
    android_assets_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "android", "app", "src", "main", "assets")
    ios_assets_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "ios", "assets")
    
    # Ensure destination directories exist
    os.makedirs(android_assets_dir, exist_ok=True)
    os.makedirs(ios_assets_dir, exist_ok=True)
    
    # Copy core files to both platforms
    core_files = [data_file, graph_file, metadata_index]
    
    for platform_dir, platform_name in [(android_assets_dir, "Android"), (ios_assets_dir, "iOS")]:
        print(f"Copying files to {platform_name} assets directory: {platform_dir}")
        
        for src_file in core_files:
            if os.path.exists(src_file):
                dest_file = os.path.join(platform_dir, os.path.basename(src_file))
                shutil.copy2(src_file, dest_file)
                print(f"  ✅ Copied {os.path.basename(src_file)}")
            else:
                print(f"  ❌ Warning: Source file not found: {src_file}")
        
        # Copy all metadata chunk files
        metadata_chunks_dir = os.path.join(output_dir, "metadata_chunks")
        platform_metadata_dir = os.path.join(platform_dir, "metadata_chunks")
        
        if os.path.exists(metadata_chunks_dir):
            # Remove existing metadata chunks directory if it exists
            if os.path.exists(platform_metadata_dir):
                shutil.rmtree(platform_metadata_dir)
            
            # Copy the entire metadata chunks directory
            shutil.copytree(metadata_chunks_dir, platform_metadata_dir)
            
            # Count copied files
            chunk_files = [f for f in os.listdir(platform_metadata_dir) if f.startswith("metadata_") and f.endswith(".json")]
            print(f"  ✅ Copied {len(chunk_files)} metadata chunk files")
        else:
            print(f"  ❌ Warning: Metadata chunks directory not found: {metadata_chunks_dir}")


def main():
    directory_path = "./docs"
    output_dir = "hnsw_rag"
    os.makedirs(output_dir, exist_ok=True)
    print(f"Using HNSW output directory: {output_dir}")

    documents = process_directory(directory_path)
    if not documents:
        print("No documents found.")
        return

    print(f"Embedding {len(documents)} document chunks...")

    doc_embeddings = []
    doc_ids = []
    doc_metadata = []
    doc_contents = []

    for doc in documents:
        try:
            embedding = embed(doc["content"])
            if len(embedding.shape) > 1:
                embedding = np.mean(embedding, axis=0)
            doc_embeddings.append(embedding)
            doc_ids.append(doc["id"])
            doc_metadata.append(doc["metadata"])
            doc_contents.append(doc["content"])
        except Exception as e:
            print(f"Error embedding doc {doc['id']}: {e}")

    if not doc_embeddings:
        print("No valid embeddings.")
        return

    embeddings_array = np.vstack(doc_embeddings).astype('float32')
    dim = embeddings_array.shape[1]

    # === Save metadata in chunks ===
    print("💾 Saving metadata in chunks...")
    metadata_documents = [
        {
            "id": doc_ids[i],
            "content": doc_contents[i],
            "metadata": doc_metadata[i]
        }
        for i in range(len(doc_ids))
    ]
    
    file_info = save_metadata_chunks(metadata_documents, output_dir)

    # === Serialize embeddings to CSV and call Rust builder ===
    csv_path = os.path.join(output_dir, "embeddings.csv")
    print(f"Saving embeddings to {csv_path}")
    with open(csv_path, "w", newline="") as f:
        writer = csv.writer(f)
        for idx, vec in enumerate(doc_embeddings):
            writer.writerow([idx] + vec.tolist())

    # Call the Rust CLI to build/dump the HNSW index
    index_path = os.path.join(output_dir, "documents_hnsw_rs.bin")
    print(f"Building HNSW index via hnsw_builder → {index_path}")
    CLI_BIN = os.path.join(
        "../", "rust", "hnsw_builder", "target", "release", "hnsw_builder"
    )

    subprocess.run([
        CLI_BIN,
        "--embeddings-csv", csv_path,
        "--output", index_path,
        "--dims", str(dim),
    ], check=True)
    print(f"HNSW index saved to {index_path}")

    # Print chunking and metadata statistics
    chunk_stats = {}
    for doc in documents:
        source = doc["metadata"]["source"]
        if source not in chunk_stats:
            chunk_stats[source] = 0
        chunk_stats[source] += 1
    
    print("\n=== Final Statistics ===")
    for source, count in chunk_stats.items():
        print(f"{source}: {count} chunks")
    print(f"Total chunks: {len(documents)}")
    print(f"Metadata files: {file_info['total_files']}")
    print(f"Avg docs per file: {file_info['docs_per_file']}")

    # === Copy files to mobile platforms ===
    copy_assets_to_mobile(output_dir)
    print("✅ Asset copying complete.")


if __name__ == "__main__":
    main()