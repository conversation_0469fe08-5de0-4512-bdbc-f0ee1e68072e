import csv
import random
import calendar
from datetime import date, timedelta, datetime
from faker import Faker # Used for generating fake retailer names
import pandas as pd

# --- CONFIGURATION ---
YEAR = 2024
START_DATE = date(YEAR, 1, 1)
END_DATE = date(YEAR, 12, 31)

# Persona & Income
GROSS_ANNUAL_SALARY = 50000.00
# Simplified Net Pay Calculation (Adjust percentages as needed for more accuracy)
TAX_RATE = 0.15 # Blended rate estimate
NI_RATE = 0.08  # Estimate
PENSION_RATE = 0.05 # Example employee contribution
NET_PAY_FACTOR = 1.0 - TAX_RATE - NI_RATE - PENSION_RATE
MONTHLY_GROSS = GROSS_ANNUAL_SALARY / 12
MONTHLY_NET_SALARY = MONTHLY_GROSS * NET_PAY_FACTOR
SALARY_PAY_DAY_OFFSET = -1 # Days before month end (e.g., -1 means last working day)

# Initial Balances
INITIAL_BANK_BALANCE = 1500.00
INITIAL_CREDIT_CARD_BALANCE = 0.00 # Assume starting fresh or paid off

# Bank Details (Synthetic)
BANK_ACCOUNT_NO = "********"
SORT_CODE = "11-22-33"

# Credit Card Details (Synthetic)
CREDIT_CARD_NO_LAST4 = "5678"
CREDIT_LIMIT = 5000.00
CREDIT_CARD_PAYMENT_DAY = 25 # Day of month bank payment is made
CREDIT_CARD_STATEMENT_DAY = 15 # Day statement period typically ends (for balance calc)

# Recurring Bank Expenses (Day of Month, Description, Amount)
RECURRING_BANK_EXPENSES = [
    (1, "DD RENTPROPERTIES", 1200.00),
    (5, "DD BRISTOL COUNCIL", 150.00), # Council Tax (often 10 installments/yr)
    (8, "DD OVO ENERGY", random.uniform(70.00, 150.00)), # Variable Utility estimate
    (15, "DD VODAFONE UK", 40.00),
    (18, "DD NETFLIX", 10.99),
    (20, "TFR SAVINGS TRANSFER", 300.00),
    (12, "DD WESSEX WATER", random.uniform(30.00, 45.00)), # Simulate quarterly variability later
]

# Variable Bank Spending Habits
AVG_DEBIT_SPEND_PER_DAY = 25.00
DEBIT_SPEND_STD_DEV = 10.00
PROB_DEBIT_SPEND_ON_DAY = 0.6 # 60% chance of spending something
PROB_ATM_WITHDRAWAL = 0.05 # 5% chance on any given day
ATM_AMOUNT_MIN = 20.00
ATM_AMOUNT_MAX = 100.00

# Variable Credit Card Spending Habits
AVG_CREDIT_SPEND_PER_DAY = 30.00
CREDIT_SPEND_STD_DEV = 15.00
PROB_CREDIT_SPEND_ON_DAY = 0.5 # 50% chance

# --- INITIALIZATION ---
fake = Faker('en_GB') # Use GB provider for more relevant names
bank_transactions = []
credit_card_transactions = []
payslip_data = []

current_bank_balance = INITIAL_BANK_BALANCE
current_credit_card_balance = INITIAL_CREDIT_CARD_BALANCE
# Track CC balance *since last statement* for payment calculation
credit_card_balance_for_payment = 0.00

# --- HELPER FUNCTIONS ---
def get_last_working_day(year, month):
    """Gets the last working day (Mon-Fri) of a month."""
    month_end = date(year, month, calendar.monthrange(year, month)[1])
    if month_end.weekday() < 5: # Mon-Fri
        return month_end
    else: # Weekend
        # Find the most recent Friday
        return month_end - timedelta(days=month_end.weekday() - 4)

def generate_plausible_description(is_debit_card=True):
    """Generates a fake retailer/transaction description."""
    if is_debit_card:
        providers = [
            fake.company() + " " + fake.random_element(elements=('Ltd', 'PLC', 'Store')),
            "POS " + fake.random_element(elements=('Tesco', 'Sainsburys', 'Asda', 'Morrisons', 'Lidl', 'Aldi')),
            "POS " + fake.random_element(elements=('Costa', 'Starbucks', 'Pret', 'Greggs')),
            "POS " + fake.random_element(elements=('Boots', 'Superdrug')),
            f"POS (Lat: {fake.latitude()}, Lng: {fake.longitude()})",  # Using latitude and longitude
            "Contactless",
        ]
    else: # Credit Card
        providers = [
            fake.company(),
            "AMAZON UK MKTPLACE", "AMZN",
            "PAYPAL *" + fake.company(),
            fake.random_element(elements=('Trainline', 'GWR', 'EasyJet', 'Ryanair')),
            fake.random_element(elements=('Deliveroo', 'Just Eat', 'Uber Eats')),
            fake.random_element(elements=('Currys', 'Argos', 'John Lewis')),
            fake.random_element(elements=('ASOS', 'Next', 'Zalando')),
        ]
    return fake.random_element(elements=providers)


# --- SIMULATION LOOP ---
current_date = START_DATE
while current_date <= END_DATE:
    day_of_month = current_date.day
    month = current_date.month
    year = current_date.year

    # --- PAYSLIP & SALARY ---
    last_working_day = get_last_working_day(year, month)
    # Adjust pay day based on offset (e.g., handle if last working day calculation needs refinement for exact pay date)
    pay_date = last_working_day # Simplified pay date = last working day for this script
    if current_date == pay_date:
        gross = round(MONTHLY_GROSS, 2)
        tax = round(gross * TAX_RATE, 2)
        ni = round(gross * NI_RATE, 2)
        pension = round(gross * PENSION_RATE, 2)
        net = round(gross - tax - ni - pension, 2)

        current_bank_balance += net
        bank_transactions.append({
            "Date": current_date,
            "Type": "BACS",
            "Description": "Salary TECH SOLUTIONS LTD",
            "Debit (£)": 0.00,
            "Credit (£)": net,
            "Balance (£)": round(current_bank_balance, 2)
        })
        payslip_data.append({
            "Pay Period End": current_date,
            "Gross Pay (£)": gross,
            "Income Tax (£)": tax,
            "National Insurance (£)": ni,
            "Pension (£)": pension,
            "Net Pay (£)": net
        })

    # --- RECURRING BANK EXPENSES ---
    for day, desc, base_amount in RECURRING_BANK_EXPENSES:
         # Simulate some variability for utilities/water
        amount = base_amount
        if "ENERGY" in desc or "WATER" in desc:
            # Simple seasonal variation example (more energy in winter)
            month_factor = 1.0 + 0.4 * abs(month - 7) / 5 # Peaking in Jan/Dec
            amount = round(random.uniform(base_amount * 0.8, base_amount * 1.2) * month_factor, 2)
            if "COUNCIL" in desc and month in [2, 3]: # Skip Council Tax in Feb/Mar (common)
                 continue

        if day_of_month == day:
            current_bank_balance -= amount
            bank_transactions.append({
                "Date": current_date,
                "Type": "DD" if "DD" in desc else "TFR" if "TFR" in desc else "PAY",
                "Description": desc,
                "Debit (£)": round(amount, 2),
                "Credit (£)": 0.00,
                "Balance (£)": round(current_bank_balance, 2)
            })

    # --- CREDIT CARD PAYMENT ---
    # Check if today is the payment day AND we have a balance from the *previous* statement period
    if day_of_month == CREDIT_CARD_PAYMENT_DAY and credit_card_balance_for_payment > 0:
         payment_amount = round(credit_card_balance_for_payment, 2)
         current_bank_balance -= payment_amount
         # Note: This payment reduces the *overall* CC balance, but the 'balance_for_payment'
         # reflects the statement amount being paid. The actual CC balance reduction happens
         # implicitly as new statement period starts.
         bank_transactions.append({
             "Date": current_date,
             "Type": "PAY",
             "Description": f"BANK B CC PAYMENT (Card ...{CREDIT_CARD_NO_LAST4})",
             "Debit (£)": payment_amount,
             "Credit (£)": 0.00,
             "Balance (£)": round(current_bank_balance, 2)
         })
         # Reset the payment tracker *after* making the payment
         credit_card_balance_for_payment = 0.00


    # --- VARIABLE BANK SPENDING (Debit Card / ATM) ---
    if random.random() < PROB_DEBIT_SPEND_ON_DAY:
        num_spends = random.randint(1, 3)
        for _ in range(num_spends):
            spend_amount = round(abs(random.normalvariate(AVG_DEBIT_SPEND_PER_DAY, DEBIT_SPEND_STD_DEV)), 2)
            if spend_amount < 1.00 : spend_amount = 1.00 # Min spend
            current_bank_balance -= spend_amount
            bank_transactions.append({
                "Date": current_date,
                "Type": "POS" if random.random() < 0.9 else "Contactless", # Type
                "Description": generate_plausible_description(is_debit_card=True),
                "Debit (£)": spend_amount,
                "Credit (£)": 0.00,
                "Balance (£)": round(current_bank_balance, 2)
            })

    if random.random() < PROB_ATM_WITHDRAWAL:
         withdraw_amount = round(random.uniform(ATM_AMOUNT_MIN, ATM_AMOUNT_MAX) / 10) * 10 # Round to nearest 10
         current_bank_balance -= withdraw_amount
         bank_transactions.append({
            "Date": current_date,
            "Type": "ATM",
            "Description": f"ATM WITHDRAWAL {fake.street_name()}",
            "Debit (£)": withdraw_amount,
            "Credit (£)": 0.00,
            "Balance (£)": round(current_bank_balance, 2)
        })

    # --- VARIABLE CREDIT CARD SPENDING ---
    if random.random() < PROB_CREDIT_SPEND_ON_DAY:
        num_spends = random.randint(1, 2)
        for _ in range(num_spends):
            spend_amount = round(abs(random.normalvariate(AVG_CREDIT_SPEND_PER_DAY, CREDIT_SPEND_STD_DEV)), 2)
            if spend_amount < 2.00 : spend_amount = 2.00 # Min spend
            if current_credit_card_balance + spend_amount <= CREDIT_LIMIT:
                 current_credit_card_balance += spend_amount
                 credit_card_transactions.append({
                     "Date": current_date,
                     "Description": generate_plausible_description(is_debit_card=False),
                     "Amount (£)": spend_amount
                 })
            # No else needed - transaction fails if over limit (simplified)

    # --- Update Credit Card Balance for Payment ---
    # On the statement day, capture the current balance to be paid next month
    if day_of_month == CREDIT_CARD_STATEMENT_DAY:
         # Payment amount is the balance accumulated *since the last statement day*.
         # This simple model sets the next payment amount to the *total* current balance
         # on statement day. A more complex model would track carry-over balances/interest.
         credit_card_balance_for_payment = current_credit_card_balance # Store amount to be paid next month

         # Reset the actual card balance as if statement cuts off and payment expected
         # (In reality balance doesn't reset, but for payment calc this simplifies it)
         # A better approach might be to calculate payment based on transactions *between* statement dates.
         # For simplicity here, we pay the full balance outstanding on statement day.
         # Let's refine: the payment amount should be the *increase* since last statement day or the full balance.
         # This part is tricky to model simply. Let's stick to paying the full balance for this script.


    # Move to next day
    current_date += timedelta(days=1)

# --- OUTPUT ---
print("Simulation Complete. Writing CSV files...")

# Bank Statement
bank_df = pd.DataFrame(bank_transactions)
bank_df = bank_df.sort_values(by="Date")
# Add initial balance row
initial_row = pd.DataFrame([{
    "Date": START_DATE - timedelta(days=1), "Type": "INFO", "Description": "Opening Balance",
    "Debit (£)": 0.00, "Credit (£)": 0.00, "Balance (£)": INITIAL_BANK_BALANCE
}])
bank_df = pd.concat([initial_row, bank_df], ignore_index=True)
bank_df.to_csv("/user_dataset/bank_statement_2024.csv", index=False, date_format='%Y-%m-%d')
print("Generated bank_statement_2024.csv")

# Credit Card Statement
cc_df = pd.DataFrame(credit_card_transactions)
cc_df = cc_df.sort_values(by="Date")
cc_df.to_csv("/user_dataset/credit_card_statement_2024.csv", index=False, date_format='%Y-%m-%d')
print("Generated credit_card_statement_2024.csv")

# Payslip Data
payslip_df = pd.DataFrame(payslip_data)
# Add Year-to-date totals (simple sum)
payslip_df['YTD Gross (£)'] = payslip_df['Gross Pay (£)'].cumsum()
payslip_df['YTD Tax (£)'] = payslip_df['Income Tax (£)'].cumsum()
payslip_df['YTD NI (£)'] = payslip_df['National Insurance (£)'].cumsum()
payslip_df['YTD Pension (£)'] = payslip_df['Pension (£)'].cumsum()
payslip_df['YTD Net (£)'] = payslip_df['Net Pay (£)'].cumsum()
payslip_df.to_csv("/user_dataset/payslips_2024.csv", index=False, date_format='%Y-%m-%d')
print("Generated payslips_2024.csv")

print("\nScript finished.")
print(f"Final Bank Balance: £{current_bank_balance:.2f}")
print(f"Final Credit Card Balance: £{current_credit_card_balance:.2f}")