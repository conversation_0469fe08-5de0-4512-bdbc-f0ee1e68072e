from transformers import AutoTokenizer, AutoModel
import torch
from onnxruntime.quantization import quantize_dynamic, QuantType
# Test the model
import onnxruntime as ort

# Load the pre-trained model and tokenizer
model_name = "sentence-transformers/all-MiniLM-L6-v2"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModel.from_pretrained(model_name)
model.eval()

# Use a realistic sequence length for your dummy input (such as 128)
# This will ensure the model can handle that sequence length
dummy_input = tokenizer(
    ["This is a sample text for export"],
    return_tensors="pt",
    padding="max_length",
    truncation=True,
    max_length=128  # Use your desired max sequence length here
)

# Export to ONNX with proper dynamic axes
torch.onnx.export(
    model,
    (dummy_input["input_ids"], dummy_input["attention_mask"]),
    "embedding_model.onnx",
    input_names=["input_ids", "attention_mask"],
    output_names=["last_hidden_state"],
    dynamic_axes={
        "input_ids": {0: "batch_size", 1: "sequence_length"},
        "attention_mask": {0: "batch_size", 1: "sequence_length"},
        "last_hidden_state": {0: "batch_size", 1: "sequence_length"}
    },
    opset_version=14,
)

# Quantize the ONNX model
quantize_dynamic("embedding_model.onnx", "embedding_model.quant.onnx", weight_type=QuantType.QInt8)

print("Model conversion complete! The quantized model is saved as 'embedding_model.quant.onnx'")

session = ort.InferenceSession("embedding_model.quant.onnx")

# Print the expected input shapes
print("\nModel input shapes:")
for i, input_info in enumerate(session.get_inputs()):
    print(f"  {i}: {input_info.name}, Shape: {input_info.shape}")

# Test with different sequence lengths
test_lengths = [4, 16, 64, 128]
for length in test_lengths:
    print(f"\nTesting with sequence length: {length}")
    test_input = tokenizer(
        ["This is a test"],
        return_tensors="np",
        padding="max_length",
        truncation=True,
        max_length=length
    )

    inputs = {
        "input_ids": test_input["input_ids"],
        "attention_mask": test_input["attention_mask"]
    }

    try:
        outputs = session.run(["last_hidden_state"], inputs)
        print(f"  Success! Output shape: {outputs[0].shape}")
    except Exception as e:
        print(f"  Failed: {e}")