<script>
Object.defineProperty(document, 'cookie', {
    get: function() { return ''; },
    set: function() { return true; }
});
if (cookieStore) {
    cookieStore = {};
}
</script>
<!DOCTYPE html><html><head>
  <meta charset="UTF-8">
  <base href="/">
  <title>Open Knowledge Repository</title>
  <meta name="viewport" content="width=device-width,minimum-scale=1">
  <meta http-equiv="cache-control" content="no-store">
<style>@charset "UTF-8";:root{--swiper-theme-color:#007aff}:root{--swiper-navigation-size:44px}</style><link rel="stylesheet" href="styles.741b2069c7ddf172.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="styles.741b2069c7ddf172.css"></noscript></head>

<body>
  <ds-app></ds-app>
<script src="runtime.c9e82ad39c6ee15a.js" type="module"></script><script src="polyfills.0a9efe389b2bba79.js" type="module"></script><script src="main.999ffb439de99b37.js" type="module"></script>




</body><!-- do not include client bundle, it is injected with Zone already loaded --></html>