import os
import json
import onnxruntime as ort
from transformers import AutoTokenizer
import numpy as np
import subprocess
import pandas as pd
import PyPDF2
import glob
import pickle
import time
import csv
import shutil
import re

# === Load tokenizer & ONNX model ===
model_path = "all-MiniLM-L6-v2.quant.onnx"
tokenizer = AutoTokenizer.from_pretrained("sentence-transformers/all-MiniLM-L6-v2")

session = ort.InferenceSession(model_path)
input_name = session.get_inputs()[0].name
attention_name = session.get_inputs()[1].name
output_name = session.get_outputs()[0].name

# Chunking parameters
CHUNK_SIZE = 1000  # Characters per chunk
CHUNK_OVERLAP = 200  # Overlap between chunks


def chunk_text(text, chunk_size=CHUNK_SIZE, overlap=CHUNK_OVERLAP):
    """
    Split text into overlapping chunks.
    
    Args:
        text: Input text to chunk
        chunk_size: Maximum characters per chunk
        overlap: Characters to overlap between chunks
    
    Returns:
        List of text chunks
    """
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    
    while start < len(text):
        # Calculate end position
        end = start + chunk_size
        
        # If this isn't the last chunk, try to break at sentence boundary
        if end < len(text):
            # Look for sentence endings within the last 200 characters
            search_start = max(start + chunk_size - 200, start)
            sentence_end = max(
                text.rfind('.', search_start, end),
                text.rfind('!', search_start, end),
                text.rfind('?', search_start, end)
            )
            
            # If we found a sentence boundary, use it
            if sentence_end > search_start:
                end = sentence_end + 1
            # Otherwise, try to break at word boundary
            else:
                last_space = text.rfind(' ', start, end)
                if last_space > start:
                    end = last_space
        
        chunk = text[start:end].strip()
        if chunk:  # Only add non-empty chunks
            chunks.append(chunk)
        
        # Move start position with overlap
        start = end - overlap
        
        # Prevent infinite loop
        if start >= end:
            start = end
    
    return chunks


def embed(texts):
    """Create embeddings for the given text using the ONNX model."""
    # Ensure texts is always a list
    if not isinstance(texts, list):
        texts = [texts]

    inputs = tokenizer(texts, return_tensors="np", padding=True, truncation=True)
    ort_inputs = {
        input_name: inputs["input_ids"],
        attention_name: inputs["attention_mask"]
    }
    ort_outs = session.run([output_name], ort_inputs)
    embeddings = ort_outs[0]

    # Normalize embeddings for cosine similarity
    norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
    # Avoid division by zero
    norms = np.maximum(norms, 1e-9)
    normalized = embeddings / norms

    # Always return the same format - vector for single input
    if len(texts) == 1:
        # For a single text, mean pool across tokens to get a single vector
        # This ensures we always return a 1D vector
        return np.mean(normalized, axis=0)
    else:
        # For multiple texts, return mean pooled vectors for each text
        return np.array([np.mean(normalized[i], axis=0) for i in range(len(texts))])


def read_pdf(file_path, password=None):
    """
    Extract text from a PDF file with multiple fallback options for corrupted files.

    Args:
        file_path: Path to the PDF file
        password: Password for encrypted PDFs (default: None)

    Returns:
        Extracted text as a string, or error message if extraction fails
    """
    # Try PyPDF2 first (most common)
    try:
        with open(file_path, 'rb') as file:
            try:
                pdf_reader = PyPDF2.PdfReader(file)

                # Handle encryption
                if pdf_reader.is_encrypted:
                    if password:
                        try:
                            pdf_reader.decrypt(password)
                        except:
                            print(f"Warning: Provided password incorrect for {file_path}")
                            return f"[Encrypted PDF: {os.path.basename(file_path)}]"

                    # Try empty password
                    if pdf_reader.is_encrypted:
                        try:
                            pdf_reader.decrypt("")
                        except:
                            print(f"Warning: Cannot decrypt PDF {file_path} - encryption requires password")
                            return f"[Encrypted PDF: {os.path.basename(file_path)}]"

                # Extract text from pages
                text = ""
                for page_num in range(len(pdf_reader.pages)):
                    try:
                        page_text = pdf_reader.pages[page_num].extract_text()
                        if page_text:
                            text += page_text + " "
                    except Exception as page_e:
                        print(f"Warning: Error extracting text from page {page_num} in {file_path}: {page_e}")

                if text.strip():
                    return text.strip()
                # If no text extracted, we'll try the next method

            except Exception as e:
                print(f"PyPDF2 couldn't process {file_path}: {e}")
                # Fall through to next method
    except Exception as file_e:
        print(f"Error opening file {file_path}: {file_e}")

    # Try pdfplumber (often works when PyPDF2 fails)
    try:
        import pdfplumber
        with pdfplumber.open(file_path, password=password) as pdf:
            text = ""
            for page in pdf.pages:
                try:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + " "
                except Exception as page_e:
                    print(f"pdfplumber error on page: {page_e}")

            if text.strip():
                print(f"Successfully extracted text using pdfplumber from {file_path}")
                return text.strip()
    except ImportError:
        print("pdfplumber not installed. Try: pip install pdfplumber")
    except Exception as e:
        print(f"pdfplumber couldn't process {file_path}: {e}")

    # Try PyMuPDF/fitz (most robust, but needs to be installed)
    try:
        import fitz
        try:
            doc = fitz.open(file_path)

            # Handle password if needed
            if doc.is_encrypted and password:
                doc.authenticate(password)

            text = ""
            for page in doc:
                try:
                    text += page.get_text() + " "
                except Exception as page_e:
                    print(f"PyMuPDF error on page: {page_e}")

            if text.strip():
                print(f"Successfully extracted text using PyMuPDF from {file_path}")
                return text.strip()
        except Exception as e:
            print(f"PyMuPDF couldn't process {file_path}: {e}")
    except ImportError:
        print("PyMuPDF not installed. Try: pip install pymupdf")

    # As a last resort, try textract (can handle many formats but has external dependencies)
    try:
        import textract
        try:
            text = textract.process(file_path, method='pdfminer').decode('utf-8')
            if text.strip():
                print(f"Successfully extracted text using textract from {file_path}")
                return text.strip()
        except Exception as e:
            print(f"textract couldn't process {file_path}: {e}")
    except ImportError:
        print("textract not installed. Try: pip install textract")

    # If we get here, all methods failed
    print(f"Failed to extract text from {file_path} using all available methods")
    return f"[Error: Could not extract text from corrupted PDF: {os.path.basename(file_path)}]"


def read_csv(file_path):
    """Extract text content from a CSV file."""
    try:
        df = pd.read_csv(file_path)
        # Convert the dataframe to a string representation
        return df.to_string(index=False)
    except Exception as e:
        print(f"Error reading CSV {file_path}: {e}")
        return ""


def read_excel(file_path):
    """Extract text content from an Excel file."""
    try:
        df = pd.read_excel(file_path)
        # Convert the dataframe to a string representation
        return df.to_string(index=False)
    except Exception as e:
        print(f"Error reading Excel {file_path}: {e}")
        return ""


def read_json(file_path):
    """Extract text content from a JSON file with the specified structure."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)

        documents = []

        for item in data:
            if "url" in item and item["url"]:
                doc_parts = [f"URL: {item['url']}"]

                if "markdown" in item and item["markdown"]:
                    doc_parts.append(f"Content: {item['markdown']}")
                else:
                    doc_parts.append("Content: [No markdown content]")

                documents.append("\n".join(doc_parts))

        return documents
    except Exception as e:
        print(f"Error reading JSON {file_path}: {e}")
        return []


def read_text(file_path):
    """Read a text file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            return file.read()
    except Exception as e:
        print(f"Error reading text file {file_path}: {e}")
        return ""


def process_directory(directory_path):
    """
    Process all supported files in a directory and return a list of chunked documents
    with their metadata.
    """
    documents = []
    doc_id = 0

    # Find all files with supported extensions
    for extension in ['*.pdf', '*.csv', '*.xlsx', '*.xls', '*.json', '*.txt']:
        file_paths = glob.glob(os.path.join(directory_path, extension))

        for file_path in file_paths:
            file_name = os.path.basename(file_path)
            ext = os.path.splitext(file_name)[1].lower()

            print(f"Processing {file_path}...")

            content = None
            file_type = None

            if ext == '.pdf':
                content = read_pdf(file_path)
                file_type = "pdf"
            elif ext == '.csv':
                content = read_csv(file_path)
                file_type = "csv"
            elif ext in ['.xlsx', '.xls']:
                content = read_excel(file_path)
                file_type = "excel"
            elif ext == '.json':
                json_docs = read_json(file_path)
                # Handle JSON documents separately since they return a list
                for i, json_content in enumerate(json_docs):
                    chunks = chunk_text(json_content)
                    print(f"  Split JSON document {i} into {len(chunks)} chunks")
                    
                    for chunk_idx, chunk in enumerate(chunks):
                        documents.append({
                            "id": f"doc_{doc_id}",
                            "content": chunk,
                            "metadata": {
                                "source": file_name,
                                "type": "json",
                                "original_index": i,
                                "chunk_index": chunk_idx,
                                "total_chunks": len(chunks)
                            }
                        })
                        doc_id += 1
                continue  # Skip the regular processing for JSON
            elif ext == '.txt':
                content = read_text(file_path)
                file_type = "text"

            # Process non-JSON files
            if content and file_type:
                chunks = chunk_text(content)
                print(f"  Split {file_name} into {len(chunks)} chunks")
                
                for chunk_idx, chunk in enumerate(chunks):
                    documents.append({
                        "id": f"doc_{doc_id}",
                        "content": chunk,
                        "metadata": {
                            "source": file_name,
                            "type": file_type,
                            "chunk_index": chunk_idx,
                            "total_chunks": len(chunks)
                        }
                    })
                    doc_id += 1

    return documents


def main():
    directory_path = "./docs"
    output_dir = "hnsw_rag"
    os.makedirs(output_dir, exist_ok=True)
    print(f"Using HNSW output directory: {output_dir}")

    documents = process_directory(directory_path)
    if not documents:
        print("No documents found.")
        return

    print(f"Embedding {len(documents)} document chunks...")

    doc_embeddings = []
    doc_ids = []
    doc_metadata = []
    doc_contents = []

    for doc in documents:
        try:
            embedding = embed(doc["content"])
            if len(embedding.shape) > 1:
                embedding = np.mean(embedding, axis=0)
            doc_embeddings.append(embedding)
            doc_ids.append(doc["id"])
            doc_metadata.append(doc["metadata"])
            doc_contents.append(doc["content"])
        except Exception as e:
            print(f"Error embedding doc {doc['id']}: {e}")

    if not doc_embeddings:
        print("No valid embeddings.")
        return

    embeddings_array = np.vstack(doc_embeddings).astype('float32')
    dim = embeddings_array.shape[1]

    # === Serialize embeddings to CSV and call Rust builder ===
    csv_path = os.path.join(output_dir, "embeddings.csv")
    print(f"Saving embeddings to {csv_path}")
    # Write rows: id, comp1, comp2, ...
    with open(csv_path, "w", newline="") as f:
        writer = csv.writer(f)
        # Use numeric IDs 0..N-1 for the Rust index builder
        for idx, vec in enumerate(doc_embeddings):
            writer.writerow([idx] + vec.tolist())

    # Call the Rust CLI to build/dump the HNSW index
    index_path = os.path.join(output_dir, "documents_hnsw_rs.bin")
    print(f"Building HNSW index via hnsw_builder → {index_path}")
    CLI_BIN = os.path.join(
        "../", "rust", "hnsw_builder", "target", "release", "hnsw_builder"
    )

    # then later:
    subprocess.run([
        CLI_BIN,
        "--embeddings-csv", csv_path,
        "--output", index_path,
        "--dims", str(dim),
    ], check=True)
    print(f"HNSW index saved to {index_path}")

    # === Save metadata ===
    metadata_path = os.path.join(output_dir, "documents_metadata.json")
    metadata_json = [
        {
            "id": doc_ids[i],
            "content": doc_contents[i],
            "metadata": doc_metadata[i]
        }
        for i in range(len(doc_ids))
    ]
    with open(metadata_path, "w", encoding="utf-8") as f:
        json.dump(metadata_json, f, ensure_ascii=False, indent=2)
    print(f"Metadata saved to {metadata_path}")

    # Print chunking statistics
    chunk_stats = {}
    for doc in documents:
        source = doc["metadata"]["source"]
        if source not in chunk_stats:
            chunk_stats[source] = 0
        chunk_stats[source] += 1
    
    print("\n=== Chunking Statistics ===")
    for source, count in chunk_stats.items():
        print(f"{source}: {count} chunks")
    print(f"Total chunks: {len(documents)}")

    # === Copy files to Android and iOS asset directories ===
    # Define source files
    data_file = os.path.join(output_dir, "documents_hnsw_rs.hnsw.data")
    graph_file = os.path.join(output_dir, "documents_hnsw_rs.hnsw.graph")
    
    # Define destination directories
    android_assets_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "android", "app", "src", "main", "assets")
    ios_assets_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "ios", "EdgeLLM", "assets")
    
    # Ensure destination directories exist
    os.makedirs(android_assets_dir, exist_ok=True)
    os.makedirs(ios_assets_dir, exist_ok=True)
    
    # Copy files to Android assets
    print(f"Copying files to Android assets directory: {android_assets_dir}")
    for src_file in [data_file, graph_file, metadata_path]:
        if os.path.exists(src_file):
            dest_file = os.path.join(android_assets_dir, os.path.basename(src_file))
            shutil.copy2(src_file, dest_file)
            print(f"  Copied {os.path.basename(src_file)}")
        else:
            print(f"  Warning: Source file not found: {src_file}")
    
    # Copy files to iOS assets
    print(f"Copying files to iOS assets directory: {ios_assets_dir}")
    for src_file in [data_file, graph_file, metadata_path]:
        if os.path.exists(src_file):
            dest_file = os.path.join(ios_assets_dir, os.path.basename(src_file))
            shutil.copy2(src_file, dest_file)
            print(f"  Copied {os.path.basename(src_file)}")
        else:
            print(f"  Warning: Source file not found: {src_file}")
    
    print("File copying complete.")


if __name__ == "__main__":
    main()