#include <jni.h>
#include <string>
#include <vector>
#include <android/log.h>

#define LOG_TAG "HNSW_JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// External declaration of the Rust functions
extern "C" {
    int hnsw_search(
        const float* query_ptr,
        size_t dim,
        size_t k,
        int* out_ptr,
        const char* dir_ptr,
        const char* base_ptr
    );

    int hnsw_build_index(
        const float* embeddings_ptr,
        size_t num_docs,
        size_t dim,
        const char* dir_ptr,
        const char* base_ptr,
        size_t m,
        size_t ef_construction
    );

    int hnsw_add_to_index(
        const float* embeddings_ptr,
        size_t num_new_docs,
        size_t dim,
        const char* dir_ptr,
        const char* base_ptr,
        size_t start_id
    );

    int hnsw_get_doc_count(
        const char* dir_ptr,
        const char* base_ptr
    );

    int hnsw_clear_cache(void);
}



extern "C" JNIEXPORT jintArray JNICALL
Java_com_smartva_HNSWModule_searchKnn(JNIEnv *env, jobject thiz, jfloatArray query_array, jint dimension, jint k, jstring index_path, jstring base_name) {
    LOGI("searchKnn called with dimension=%d, k=%d", dimension, k);

    // Convert Java strings to C strings
    const char* index_path_str = env->GetStringUTFChars(index_path, nullptr);
    const char* base_name_str = env->GetStringUTFChars(base_name, nullptr);

    // Convert Java float array to C array
    jfloat* query_data = env->GetFloatArrayElements(query_array, nullptr);
    jsize query_length = env->GetArrayLength(query_array);

    LOGI("Query array length: %d, expected dimension: %d", query_length, dimension);
    LOGI("Index path: %s, base name: %s", index_path_str, base_name_str);

    jintArray result = nullptr;

    try {
        // Prepare output buffer for results
        std::vector<int> results(k);

        LOGI("Calling Rust hnsw_search function directly");

        // Call the Rust function directly (now linked statically)
        int status = hnsw_search(
                query_data,
                static_cast<size_t>(dimension),
                static_cast<size_t>(k),
                results.data(),
                index_path_str,
                base_name_str
        );

        if (status != 0) {
            LOGE("hnsw_search failed with status: %d", status);
            jclass exceptionClass = env->FindClass("java/lang/RuntimeException");
            env->ThrowNew(exceptionClass, "Failed to search the HNSW index");
            return nullptr;
        }

        LOGI("hnsw_search succeeded, creating result array");

        // Create Java int array with the results
        result = env->NewIntArray(k);
        if (result != nullptr) {
            env->SetIntArrayRegion(result, 0, k, results.data());
            LOGI("Successfully created result array with %d elements", k);
        } else {
            LOGE("Failed to create result array");
        }

    } catch (const std::exception& e) {
        LOGE("Exception in searchKnn: %s", e.what());
        jclass exceptionClass = env->FindClass("java/lang/RuntimeException");
        env->ThrowNew(exceptionClass, e.what());
    }

    // Clean up
    env->ReleaseFloatArrayElements(query_array, query_data, JNI_ABORT);
    env->ReleaseStringUTFChars(index_path, index_path_str);
    env->ReleaseStringUTFChars(base_name, base_name_str);

    return result;
}

extern "C" JNIEXPORT jint JNICALL
Java_com_smartva_HNSWModule_buildIndex(JNIEnv *env, jobject thiz, jfloatArray embeddings_array, jint num_docs, jint dimension, jstring index_path, jstring base_name, jint m, jint ef_construction) {
    LOGI("buildIndex called with num_docs=%d, dimension=%d", num_docs, dimension);

    // Convert Java strings to C strings
    const char* index_path_str = env->GetStringUTFChars(index_path, nullptr);
    const char* base_name_str = env->GetStringUTFChars(base_name, nullptr);

    // Convert Java float array to C array
    jfloat* embeddings_data = env->GetFloatArrayElements(embeddings_array, nullptr);
    jsize embeddings_length = env->GetArrayLength(embeddings_array);

    LOGI("Embeddings array length: %d, expected: %d", embeddings_length, num_docs * dimension);

    int status = -1;

    try {
        // Call the Rust function
        status = hnsw_build_index(
            embeddings_data,
            static_cast<size_t>(num_docs),
            static_cast<size_t>(dimension),
            index_path_str,
            base_name_str,
            static_cast<size_t>(m),
            static_cast<size_t>(ef_construction)
        );

        if (status != 0) {
            LOGE("hnsw_build_index failed with status: %d", status);
        } else {
            LOGI("hnsw_build_index succeeded");
        }

    } catch (const std::exception& e) {
        LOGE("Exception in buildIndex: %s", e.what());
        status = -1;
    }

    // Clean up
    env->ReleaseFloatArrayElements(embeddings_array, embeddings_data, JNI_ABORT);
    env->ReleaseStringUTFChars(index_path, index_path_str);
    env->ReleaseStringUTFChars(base_name, base_name_str);

    return status;
}

extern "C" JNIEXPORT jint JNICALL
Java_com_smartva_HNSWModule_addToIndex(JNIEnv *env, jobject thiz, jfloatArray embeddings_array, jint num_new_docs, jint dimension, jstring index_path, jstring base_name, jint start_id) {
    LOGI("addToIndex called with num_new_docs=%d, dimension=%d, start_id=%d", num_new_docs, dimension, start_id);

    // Convert Java strings to C strings
    const char* index_path_str = env->GetStringUTFChars(index_path, nullptr);
    const char* base_name_str = env->GetStringUTFChars(base_name, nullptr);

    // Convert Java float array to C array
    jfloat* embeddings_data = env->GetFloatArrayElements(embeddings_array, nullptr);
    jsize embeddings_length = env->GetArrayLength(embeddings_array);

    LOGI("Embeddings array length: %d, expected: %d", embeddings_length, num_new_docs * dimension);

    int status = -1;

    try {
        // Call the Rust function
        status = hnsw_add_to_index(
            embeddings_data,
            static_cast<size_t>(num_new_docs),
            static_cast<size_t>(dimension),
            index_path_str,
            base_name_str,
            static_cast<size_t>(start_id)
        );

        if (status != 0) {
            LOGE("hnsw_add_to_index failed with status: %d", status);
        } else {
            LOGI("hnsw_add_to_index succeeded");
        }

    } catch (const std::exception& e) {
        LOGE("Exception in addToIndex: %s", e.what());
        status = -1;
    }

    // Clean up
    env->ReleaseFloatArrayElements(embeddings_array, embeddings_data, JNI_ABORT);
    env->ReleaseStringUTFChars(index_path, index_path_str);
    env->ReleaseStringUTFChars(base_name, base_name_str);

    return status;
}

extern "C" JNIEXPORT jint JNICALL
Java_com_smartva_HNSWModule_getDocumentCount(JNIEnv *env, jobject thiz, jstring index_path, jstring base_name) {
    LOGI("getDocumentCount called");

    // Convert Java strings to C strings
    const char* index_path_str = env->GetStringUTFChars(index_path, nullptr);
    const char* base_name_str = env->GetStringUTFChars(base_name, nullptr);

    int count = -1;

    try {
        // Call the Rust function
        count = hnsw_get_doc_count(index_path_str, base_name_str);

        if (count < 0) {
            LOGE("hnsw_get_doc_count failed with count: %d", count);
        } else {
            LOGI("hnsw_get_doc_count succeeded, count: %d", count);
        }

    } catch (const std::exception& e) {
        LOGE("Exception in getDocumentCount: %s", e.what());
        count = -1;
    }

    // Clean up
    env->ReleaseStringUTFChars(index_path, index_path_str);
    env->ReleaseStringUTFChars(base_name, base_name_str);

    return count;
}

extern "C" JNIEXPORT jint JNICALL
Java_com_smartva_HNSWModule_clearCache(JNIEnv *env, jobject thiz) {
    LOGI("clearCache called");

    int status = -1;

    try {
        // Call the Rust function
        status = hnsw_clear_cache();

        if (status != 0) {
            LOGE("hnsw_clear_cache failed with status: %d", status);
        } else {
            LOGI("hnsw_clear_cache succeeded");
        }

    } catch (const std::exception& e) {
        LOGE("Exception in clearCache: %s", e.what());
        status = -1;
    }

    return status;
}