#include <jni.h>
#include <string>
#include <vector>
#include <android/log.h>

#define LOG_TAG "HNSW_JNI"
#define LOGI(...) __android_log_print(ANDROID_LOG_INFO, LOG_TAG, __VA_ARGS__)
#define LOGE(...) __android_log_print(ANDROID_LOG_ERROR, LOG_TAG, __VA_ARGS__)

// External declaration of the Rust function
extern "C" {
    int hnsw_search(
        const float* query_ptr,
        size_t dim,
        size_t k,
        int* out_ptr,
        const char* dir_ptr,
        const char* base_ptr
    );
}



extern "C" JNIEXPORT jintArray JNICALL
Java_com_smartva_HNSWModule_searchKnn(JNIEnv *env, jobject thiz, jfloatArray query_array, jint dimension, jint k, jstring index_path, jstring base_name) {
    LOGI("searchKnn called with dimension=%d, k=%d", dimension, k);

    // Convert Java strings to C strings
    const char* index_path_str = env->GetStringUTFChars(index_path, nullptr);
    const char* base_name_str = env->GetStringUTFChars(base_name, nullptr);

    // Convert Java float array to C array
    jfloat* query_data = env->GetFloatArrayElements(query_array, nullptr);
    jsize query_length = env->GetArrayLength(query_array);

    LOGI("Query array length: %d, expected dimension: %d", query_length, dimension);
    LOGI("Index path: %s, base name: %s", index_path_str, base_name_str);

    jintArray result = nullptr;

    try {
        // Prepare output buffer for results
        std::vector<int> results(k);

        LOGI("Calling Rust hnsw_search function directly");

        // Call the Rust function directly (now linked statically)
        int status = hnsw_search(
                query_data,
                static_cast<size_t>(dimension),
                static_cast<size_t>(k),
                results.data(),
                index_path_str,
                base_name_str
        );

        if (status != 0) {
            LOGE("hnsw_search failed with status: %d", status);
            jclass exceptionClass = env->FindClass("java/lang/RuntimeException");
            env->ThrowNew(exceptionClass, "Failed to search the HNSW index");
            return nullptr;
        }

        LOGI("hnsw_search succeeded, creating result array");

        // Create Java int array with the results
        result = env->NewIntArray(k);
        if (result != nullptr) {
            env->SetIntArrayRegion(result, 0, k, results.data());
            LOGI("Successfully created result array with %d elements", k);
        } else {
            LOGE("Failed to create result array");
        }

    } catch (const std::exception& e) {
        LOGE("Exception in searchKnn: %s", e.what());
        jclass exceptionClass = env->FindClass("java/lang/RuntimeException");
        env->ThrowNew(exceptionClass, e.what());
    }

    // Clean up
    env->ReleaseFloatArrayElements(query_array, query_data, JNI_ABORT);
    env->ReleaseStringUTFChars(index_path, index_path_str);
    env->ReleaseStringUTFChars(base_name, base_name_str);

    return result;
}