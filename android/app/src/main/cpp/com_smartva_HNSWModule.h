/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_smartva_HNSWModule */

#ifndef _Included_com_edgellm_HNSWModule
#define _Included_com_edgellm_HNSWModule
#ifdef __cplusplus
extern "C" {
#endif

/*
 * Class:     com_smartva_HNSWModule
 * Method:    searchKnn
 * Signature: ([FIIL java/lang/String;Ljava/lang/String;)[I
 */
JNIEXPORT jintArray JNICALL Java_com_smartva_HNSWModule_searchKnn
  (JNIEnv *, jobject, jfloatArray, jint, jint, jstring, jstring);

/*
 * Class:     com_smartva_HNSWModule
 * Method:    buildIndex
 * Signature: ([FIILjava/lang/String;Ljava/lang/String;II)I
 */
JNIEXPORT jint JNICALL Java_com_smartva_HNSWModule_buildIndex
  (JNIEnv *, jobject, jfloatArray, jint, jint, jstring, jstring, jint, jint);

/*
 * Class:     com_smartva_HNSWModule
 * Method:    addToIndex
 * Signature: ([FIILjava/lang/String;Ljava/lang/String;I)I
 */
JNIEXPORT jint JNICALL Java_com_smartva_HNSWModule_addToIndex
  (JNIEnv *, jobject, jfloatArray, jint, jint, jstring, jstring, jint);

/*
 * Class:     com_smartva_HNSWModule
 * Method:    getDocumentCount
 * Signature: (Ljava/lang/String;Ljava/lang/String;)I
 */
JNIEXPORT jint JNICALL Java_com_smartva_HNSWModule_getDocumentCount
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_smartva_HNSWModule
 * Method:    clearCache
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_smartva_HNSWModule_clearCache
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
