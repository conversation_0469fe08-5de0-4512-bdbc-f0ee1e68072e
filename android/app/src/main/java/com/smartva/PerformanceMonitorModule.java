package com.smartva;

import android.app.ActivityManager;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.BatteryManager;
import android.os.Build;
import android.os.Debug;
import android.os.PowerManager;
import android.os.StatFs;
import android.os.Environment;
import android.system.Os;
import android.system.OsConstants;

import com.facebook.react.bridge.Arguments;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.WritableMap;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.io.RandomAccessFile;

public class PerformanceMonitorModule extends ReactContextBaseJavaModule {
    private static final String MODULE_NAME = "PerformanceMonitorAndroid";
    private final ReactApplicationContext reactContext;

    public PerformanceMonitorModule(ReactApplicationContext reactContext) {
        super(reactContext);
        this.reactContext = reactContext;
    }

    @Override
    public String getName() {
        return MODULE_NAME;
    }

    @ReactMethod
    public void getCurrentMetrics(Promise promise) {
        try {
            WritableMap metrics = Arguments.createMap();

            // Memory metrics
            WritableMap memoryInfo = getMemoryInfo();
            metrics.putMap("memory", memoryInfo);

            // CPU metrics
            WritableMap cpuInfo = getCPUInfo();
            metrics.putMap("cpu", cpuInfo);

            // Process metrics
            WritableMap processInfo = getProcessInfo();
            metrics.putMap("process", processInfo);

            // Energy metrics
            WritableMap energyInfo = getEnergyInfo();
            metrics.putMap("energy", energyInfo);

            promise.resolve(metrics);
        } catch (Exception e) {
            promise.reject("METRICS_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void startOperationTracking(String operationName, Promise promise) {
        try {
            WritableMap startMetrics = Arguments.createMap();

            // Capture baseline metrics before operation
            startMetrics.putMap("memory", getMemoryInfo());
            startMetrics.putMap("cpu", getCPUInfo());
            startMetrics.putMap("process", getProcessInfo());
            startMetrics.putDouble("timestamp", System.currentTimeMillis());
            startMetrics.putString("operationName", operationName);

            promise.resolve(startMetrics);
        } catch (Exception e) {
            promise.reject("OPERATION_TRACKING_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void endOperationTracking(String operationName, Promise promise) {
        try {
            WritableMap endMetrics = Arguments.createMap();

            // Capture metrics after operation
            endMetrics.putMap("memory", getMemoryInfo());
            endMetrics.putMap("cpu", getCPUInfo());
            endMetrics.putMap("process", getProcessInfo());
            endMetrics.putDouble("timestamp", System.currentTimeMillis());
            endMetrics.putString("operationName", operationName);

            promise.resolve(endMetrics);
        } catch (Exception e) {
            promise.reject("OPERATION_TRACKING_ERROR", e.getMessage(), e);
        }
    }

    private WritableMap getMemoryInfo() {
        WritableMap memoryInfo = Arguments.createMap();

        try {
            // System memory info
            ActivityManager activityManager = (ActivityManager) reactContext.getSystemService(Context.ACTIVITY_SERVICE);
            ActivityManager.MemoryInfo systemMemInfo = new ActivityManager.MemoryInfo();
            activityManager.getMemoryInfo(systemMemInfo);

            long totalMemory = systemMemInfo.totalMem;
            long availableMemory = systemMemInfo.availMem;
            long usedMemory = totalMemory - availableMemory;

            memoryInfo.putDouble("systemTotalMB", totalMemory / (1024.0 * 1024.0));
            memoryInfo.putDouble("systemUsedMB", usedMemory / (1024.0 * 1024.0));
            memoryInfo.putDouble("systemFreeMB", availableMemory / (1024.0 * 1024.0));
            memoryInfo.putBoolean("lowMemory", systemMemInfo.lowMemory);
            memoryInfo.putDouble("threshold", systemMemInfo.threshold / (1024.0 * 1024.0));

            // Process memory info - Enhanced for native libraries
            Debug.MemoryInfo processMemInfo = new Debug.MemoryInfo();
            Debug.getMemoryInfo(processMemInfo);

            memoryInfo.putDouble("processPrivateDirtyMB", processMemInfo.getTotalPrivateDirty() / 1024.0);
            memoryInfo.putDouble("processPrivateCleanMB", processMemInfo.getTotalPrivateClean() / 1024.0);
            memoryInfo.putDouble("processSharedDirtyMB", processMemInfo.getTotalSharedDirty() / 1024.0);
            memoryInfo.putDouble("processSharedCleanMB", processMemInfo.getTotalSharedClean() / 1024.0);
            memoryInfo.putDouble("processPssMB", processMemInfo.getTotalPss() / 1024.0);

            // Enhanced native heap tracking
            long nativeHeapSize = Debug.getNativeHeapSize();
            long nativeHeapAllocated = Debug.getNativeHeapAllocatedSize();
            long nativeHeapFree = Debug.getNativeHeapFreeSize();

            memoryInfo.putDouble("nativeHeapSizeMB", nativeHeapSize / (1024.0 * 1024.0));
            memoryInfo.putDouble("nativeHeapAllocatedMB", nativeHeapAllocated / (1024.0 * 1024.0));
            memoryInfo.putDouble("nativeHeapFreeMB", nativeHeapFree / (1024.0 * 1024.0));

            // Java heap
            Runtime runtime = Runtime.getRuntime();
            long javaHeapMax = runtime.maxMemory();
            long javaHeapTotal = runtime.totalMemory();
            long javaHeapFree = runtime.freeMemory();
            long javaHeapUsed = javaHeapTotal - javaHeapFree;

            memoryInfo.putDouble("javaHeapMaxMB", javaHeapMax / (1024.0 * 1024.0));
            memoryInfo.putDouble("javaHeapTotalMB", javaHeapTotal / (1024.0 * 1024.0));
            memoryInfo.putDouble("javaHeapUsedMB", javaHeapUsed / (1024.0 * 1024.0));
            memoryInfo.putDouble("javaHeapFreeMB", javaHeapFree / (1024.0 * 1024.0));

            // Additional memory tracking for native libraries
            try {
                // Get detailed memory breakdown including native libraries
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                    Debug.MemoryInfo[] processMemInfoArray = activityManager.getProcessMemoryInfo(new int[]{android.os.Process.myPid()});
                    if (processMemInfoArray.length > 0) {
                        Debug.MemoryInfo detailedMemInfo = processMemInfoArray[0];

                        // Native library memory (includes llama.cpp, ONNX, etc.)
                        try {
                            String nativeHeapStr = detailedMemInfo.getMemoryStat("summary.native-heap");
                            if (nativeHeapStr != null && !nativeHeapStr.isEmpty()) {
                                memoryInfo.putDouble("nativeLibrariesMB", Double.parseDouble(nativeHeapStr) / 1024.0);
                            }
                        } catch (Exception e) {
                            // Native heap stat not available
                        }

                        try {
                            String graphicsStr = detailedMemInfo.getMemoryStat("summary.graphics");
                            if (graphicsStr != null && !graphicsStr.isEmpty()) {
                                memoryInfo.putDouble("graphicsMB", Double.parseDouble(graphicsStr) / 1024.0);
                            }
                        } catch (Exception e) {
                            // Graphics stat not available
                        }

                        try {
                            String codeStr = detailedMemInfo.getMemoryStat("summary.code");
                            if (codeStr != null && !codeStr.isEmpty()) {
                                memoryInfo.putDouble("codeMB", Double.parseDouble(codeStr) / 1024.0);
                            }
                        } catch (Exception e) {
                            // Code stat not available
                        }

                        try {
                            String stackStr = detailedMemInfo.getMemoryStat("summary.stack");
                            if (stackStr != null && !stackStr.isEmpty()) {
                                memoryInfo.putDouble("stackMB", Double.parseDouble(stackStr) / 1024.0);
                            }
                        } catch (Exception e) {
                            // Stack stat not available
                        }

                        // Total proportional set size (more accurate than simple PSS)
                        memoryInfo.putDouble("totalPssMB", detailedMemInfo.getTotalPss() / 1024.0);
                    }
                }
            } catch (Exception e) {
                // Detailed memory stats not available on this device
                memoryInfo.putString("detailedMemoryError", e.getMessage());
            }

            // Memory pressure indicators
            memoryInfo.putDouble("memoryPressure", calculateMemoryPressure(systemMemInfo));

        } catch (Exception e) {
            memoryInfo.putString("error", e.getMessage());
        }

        return memoryInfo;
    }

    private double calculateMemoryPressure(ActivityManager.MemoryInfo memInfo) {
        // Calculate memory pressure as a percentage (0-100)
        // Higher values indicate more memory pressure
        double availableRatio = (double) memInfo.availMem / memInfo.totalMem;
        double pressure = (1.0 - availableRatio) * 100.0;

        // Adjust for low memory threshold
        if (memInfo.lowMemory) {
            pressure = Math.max(pressure, 85.0); // At least 85% pressure if low memory
        }

        return Math.min(pressure, 100.0);
    }

    private WritableMap getCPUInfo() {
        WritableMap cpuInfo = Arguments.createMap();

        try {
            // CPU core count
            int cores = Runtime.getRuntime().availableProcessors();
            cpuInfo.putInt("cores", cores);

            // Enhanced CPU usage tracking
            double systemCpuUsage = getSystemCPUUsage();
            double processCpuUsage = getProcessCPUUsage();

            cpuInfo.putDouble("systemUsage", systemCpuUsage);
            cpuInfo.putDouble("processUsage", processCpuUsage);
            cpuInfo.putDouble("usage", processCpuUsage); // Keep for backward compatibility

            // CPU frequency (if available)
            try {
                String freqStr = readFile("/sys/devices/system/cpu/cpu0/cpufreq/scaling_cur_freq");
                if (freqStr != null) {
                    long frequency = Long.parseLong(freqStr.trim()) / 1000; // Convert to MHz
                    cpuInfo.putDouble("frequencyMHz", frequency);
                }

                // Try to get max frequency as well
                String maxFreqStr = readFile("/sys/devices/system/cpu/cpu0/cpufreq/cpuinfo_max_freq");
                if (maxFreqStr != null) {
                    long maxFrequency = Long.parseLong(maxFreqStr.trim()) / 1000;
                    cpuInfo.putDouble("maxFrequencyMHz", maxFrequency);
                }
            } catch (Exception e) {
                // CPU frequency not available
            }

            // Load average
            try {
                String loadavg = readFile("/proc/loadavg");
                if (loadavg != null) {
                    String[] parts = loadavg.split(" ");
                    if (parts.length >= 3) {
                        cpuInfo.putDouble("loadAvg1min", Double.parseDouble(parts[0]));
                        cpuInfo.putDouble("loadAvg5min", Double.parseDouble(parts[1]));
                        cpuInfo.putDouble("loadAvg15min", Double.parseDouble(parts[2]));
                    }
                }
            } catch (Exception e) {
                // Load average not available
            }

            // Thread count for this process
            try {
                String status = readFile("/proc/self/status");
                if (status != null) {
                    String[] lines = status.split("\n");
                    for (String line : lines) {
                        if (line.startsWith("Threads:")) {
                            String threadCount = line.split("\\s+")[1];
                            cpuInfo.putInt("threadCount", Integer.parseInt(threadCount));
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                // Thread count not available
            }

        } catch (Exception e) {
            cpuInfo.putString("error", e.getMessage());
        }

        return cpuInfo;
    }

    private WritableMap getProcessInfo() {
        WritableMap processInfo = Arguments.createMap();
        
        try {
            processInfo.putInt("pid", android.os.Process.myPid());
            processInfo.putInt("uid", android.os.Process.myUid());
            
            // Thread count
            try {
                String status = readFile("/proc/self/status");
                if (status != null) {
                    String[] lines = status.split("\n");
                    for (String line : lines) {
                        if (line.startsWith("Threads:")) {
                            String threadCount = line.split("\\s+")[1];
                            processInfo.putInt("threadCount", Integer.parseInt(threadCount));
                            break;
                        }
                    }
                }
            } catch (Exception e) {
                // Thread count not available
            }
            
            // Process uptime
            try {
                String stat = readFile("/proc/self/stat");
                if (stat != null) {
                    String[] parts = stat.split(" ");
                    if (parts.length > 21) {
                        long starttime = Long.parseLong(parts[21]);
                        long uptime = System.currentTimeMillis() / 1000 - starttime / 100; // Convert to seconds
                        processInfo.putDouble("uptimeSeconds", uptime);
                    }
                }
            } catch (Exception e) {
                // Uptime not available
            }
            
        } catch (Exception e) {
            processInfo.putString("error", e.getMessage());
        }
        
        return processInfo;
    }

    private WritableMap getEnergyInfo() {
        WritableMap energyInfo = Arguments.createMap();
        
        try {
            // Battery information
            IntentFilter ifilter = new IntentFilter(Intent.ACTION_BATTERY_CHANGED);
            Intent batteryStatus = reactContext.registerReceiver(null, ifilter);
            
            if (batteryStatus != null) {
                int level = batteryStatus.getIntExtra(BatteryManager.EXTRA_LEVEL, -1);
                int scale = batteryStatus.getIntExtra(BatteryManager.EXTRA_SCALE, -1);
                float batteryPct = level * 100 / (float) scale;
                
                energyInfo.putDouble("batteryLevel", batteryPct);
                
                int status = batteryStatus.getIntExtra(BatteryManager.EXTRA_STATUS, -1);
                boolean isCharging = status == BatteryManager.BATTERY_STATUS_CHARGING ||
                                   status == BatteryManager.BATTERY_STATUS_FULL;
                energyInfo.putBoolean("isCharging", isCharging);
                
                int plugged = batteryStatus.getIntExtra(BatteryManager.EXTRA_PLUGGED, -1);
                String chargingMethod = "none";
                if (plugged == BatteryManager.BATTERY_PLUGGED_USB) {
                    chargingMethod = "usb";
                } else if (plugged == BatteryManager.BATTERY_PLUGGED_AC) {
                    chargingMethod = "ac";
                } else if (plugged == BatteryManager.BATTERY_PLUGGED_WIRELESS) {
                    chargingMethod = "wireless";
                }
                energyInfo.putString("chargingMethod", chargingMethod);
                
                int temperature = batteryStatus.getIntExtra(BatteryManager.EXTRA_TEMPERATURE, -1);
                energyInfo.putDouble("batteryTemperature", temperature / 10.0); // Convert to Celsius
            }
            
            // Power save mode
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                PowerManager powerManager = (PowerManager) reactContext.getSystemService(Context.POWER_SERVICE);
                if (powerManager != null) {
                    energyInfo.putBoolean("powerSaveMode", powerManager.isPowerSaveMode());
                }
            }
            
        } catch (Exception e) {
            energyInfo.putString("error", e.getMessage());
        }
        
        return energyInfo;
    }

    private double getSystemCPUUsage() {
        try {
            RandomAccessFile reader = new RandomAccessFile("/proc/stat", "r");
            String load = reader.readLine();
            reader.close();

            String[] toks = load.split(" +");
            long idle1 = Long.parseLong(toks[4]);
            long cpu1 = Long.parseLong(toks[2]) + Long.parseLong(toks[3]) + Long.parseLong(toks[5])
                      + Long.parseLong(toks[6]) + Long.parseLong(toks[7]) + Long.parseLong(toks[8]);

            try {
                Thread.sleep(100); // Reduced sleep time for more responsive monitoring
            } catch (Exception e) {}

            reader = new RandomAccessFile("/proc/stat", "r");
            load = reader.readLine();
            reader.close();

            toks = load.split(" +");
            long idle2 = Long.parseLong(toks[4]);
            long cpu2 = Long.parseLong(toks[2]) + Long.parseLong(toks[3]) + Long.parseLong(toks[5])
                      + Long.parseLong(toks[6]) + Long.parseLong(toks[7]) + Long.parseLong(toks[8]);

            return (double)(cpu2 - cpu1) / ((cpu2 + idle2) - (cpu1 + idle1)) * 100.0;

        } catch (IOException ex) {
            return 0.0;
        }
    }

    private double getProcessCPUUsage() {
        try {
            // Get process-specific CPU usage from /proc/self/stat
            String stat = readFile("/proc/self/stat");
            if (stat != null) {
                String[] parts = stat.split(" ");
                if (parts.length > 15) {
                    long utime = Long.parseLong(parts[13]); // User time
                    long stime = Long.parseLong(parts[14]); // System time
                    long cutime = Long.parseLong(parts[15]); // Children user time
                    long cstime = Long.parseLong(parts[16]); // Children system time

                    long totalTime = utime + stime + cutime + cstime;

                    // Get system uptime
                    String uptimeStr = readFile("/proc/uptime");
                    if (uptimeStr != null) {
                        double uptime = Double.parseDouble(uptimeStr.split(" ")[0]);
                        long hertz = 100; // Typical value, could be read from /proc/stat

                        double processTime = (double) totalTime / hertz;
                        double cpuUsage = (processTime / uptime) * 100.0;

                        return Math.min(cpuUsage, 100.0); // Cap at 100%
                    }
                }
            }
            return 0.0;
        } catch (Exception e) {
            return 0.0;
        }
    }

    // Keep the old method for backward compatibility
    private double getCPUUsage() {
        return getProcessCPUUsage();
    }

    private String readFile(String path) {
        try {
            BufferedReader reader = new BufferedReader(new FileReader(path));
            String line = reader.readLine();
            reader.close();
            return line;
        } catch (IOException e) {
            return null;
        }
    }
}
