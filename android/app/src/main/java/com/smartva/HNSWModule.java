package com.smartva;

import com.facebook.react.bridge.NativeModule;
import com.facebook.react.bridge.ReactApplicationContext;
import com.facebook.react.bridge.ReactContextBaseJavaModule;
import com.facebook.react.bridge.ReactMethod;
import com.facebook.react.bridge.Promise;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.bridge.WritableMap;
import com.facebook.react.bridge.WritableArray;
import com.facebook.react.bridge.Arguments;

public class HNSWModule extends ReactContextBaseJavaModule {
    static {
        try {
            System.loadLibrary("hnsw_native");
            android.util.Log.i("HNSWModule", "Successfully loaded hnsw_native library");
        } catch (UnsatisfiedLinkError e) {
            android.util.Log.e("HNSWModule", "Failed to load hnsw_native library", e);
            throw e;
        }
    }

    public HNSWModule(ReactApplicationContext context) {
        super(context);
        android.util.Log.i("HNSWModule", "HNSWModule constructor called");
    }

    @Override
    public String getName() {
        return "HnswSearchModule";
    }

    // Native methods that interface with your Rust library
    private native int[] searchKnn(float[] query, int dimension, int k, String indexPath, String baseName);
    private native int buildIndex(float[] embeddings, int numDocs, int dimension, String indexPath, String baseName, int m, int efConstruction);
    private native int addToIndex(float[] embeddings, int numNewDocs, int dimension, String indexPath, String baseName, int startId);
    private native int getDocumentCount(String indexPath, String baseName);
    private native int clearCache();

    @ReactMethod
    public void searchKnn(ReadableArray queryArray, int dimension, int k, String indexPath, String baseName, Promise promise) {
        android.util.Log.i("HNSWModule", "searchKnn called with dimension=" + dimension + ", k=" + k + ", indexPath=" + indexPath);
        try {
            // Convert ReadableArray to float array
            float[] query = new float[queryArray.size()];
            for (int i = 0; i < queryArray.size(); i++) {
                query[i] = (float) queryArray.getDouble(i);
            }

            // Call native search function
            int[] results = searchKnn(query, dimension, k, indexPath, baseName);

            // Create result object matching JavaScript expectations
            WritableMap resultMap = Arguments.createMap();
            WritableArray neighborsArray = Arguments.createArray();
            WritableArray distancesArray = Arguments.createArray();

            // Results contains only neighbor IDs (not distances)
            for (int i = 0; i < results.length; i++) {
                neighborsArray.pushInt(results[i]);
                distancesArray.pushDouble(0.0); // Placeholder distance since Rust function doesn't return distances
            }

            resultMap.putArray("neighbors", neighborsArray);
            resultMap.putArray("distances", distancesArray);

            promise.resolve(resultMap);
        } catch (Exception e) {
            promise.reject("SEARCH_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void buildIndex(ReadableArray embeddingsArray, int numDocs, int dimension, String indexPath, String baseName, int m, int efConstruction, Promise promise) {
        android.util.Log.i("HNSWModule", "buildIndex called with numDocs=" + numDocs + ", dimension=" + dimension);
        try {
            // Convert ReadableArray to float array
            float[] embeddings = new float[embeddingsArray.size()];
            for (int i = 0; i < embeddingsArray.size(); i++) {
                embeddings[i] = (float) embeddingsArray.getDouble(i);
            }

            // Call native build function
            int result = buildIndex(embeddings, numDocs, dimension, indexPath, baseName, m, efConstruction);

            if (result != 0) {
                promise.reject("HNSW_ERROR", "Failed to build index");
                return;
            }

            WritableMap resultMap = Arguments.createMap();
            resultMap.putBoolean("success", true);
            resultMap.putString("message", "Index built successfully");
            promise.resolve(resultMap);
        } catch (Exception e) {
            promise.reject("BUILD_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void addToIndex(ReadableArray embeddingsArray, int numNewDocs, int dimension, String indexPath, String baseName, int startId, Promise promise) {
        android.util.Log.i("HNSWModule", "addToIndex called with numNewDocs=" + numNewDocs + ", dimension=" + dimension);
        try {
            // Convert ReadableArray to float array
            float[] embeddings = new float[embeddingsArray.size()];
            for (int i = 0; i < embeddingsArray.size(); i++) {
                embeddings[i] = (float) embeddingsArray.getDouble(i);
            }

            // Call native add function
            int result = addToIndex(embeddings, numNewDocs, dimension, indexPath, baseName, startId);

            if (result != 0) {
                promise.reject("HNSW_ERROR", "Failed to add to index");
                return;
            }

            WritableMap resultMap = Arguments.createMap();
            resultMap.putBoolean("success", true);
            resultMap.putString("message", "Documents added to index successfully");
            promise.resolve(resultMap);
        } catch (Exception e) {
            promise.reject("ADD_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void getDocumentCount(String indexPath, String baseName, Promise promise) {
        android.util.Log.i("HNSWModule", "getDocumentCount called for path=" + indexPath);
        try {
            // Call native count function
            int count = getDocumentCount(indexPath, baseName);

            if (count < 0) {
                promise.reject("HNSW_ERROR", "Failed to get document count");
                return;
            }

            WritableMap resultMap = Arguments.createMap();
            resultMap.putInt("count", count);
            promise.resolve(resultMap);
        } catch (Exception e) {
            promise.reject("COUNT_ERROR", e.getMessage(), e);
        }
    }

    @ReactMethod
    public void clearCache(Promise promise) {
        android.util.Log.i("HNSWModule", "clearCache called");
        try {
            // Call native clear function
            int result = clearCache();

            if (result != 0) {
                promise.reject("HNSW_ERROR", "Failed to clear cache");
                return;
            }

            WritableMap resultMap = Arguments.createMap();
            resultMap.putBoolean("success", true);
            resultMap.putString("message", "Cache cleared successfully");
            promise.resolve(resultMap);
        } catch (Exception e) {
            promise.reject("CLEAR_ERROR", e.getMessage(), e);
        }
    }
}