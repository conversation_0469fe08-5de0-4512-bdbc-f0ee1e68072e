System Architecture 

The SmartVA system implements Retrieval Augmented Generation (RAG) entirely on-device, eliminating the need for cloud-based inference. The architecture, shown in Figure 1, leverages a modular design integrating cross-platform capabilities with high-performance native libraries.

![Figure 1.High-Level System Architecture of Mobile Edge RAG Deployment. The system operates entirely within an offline security boundary, eliminating external data transmission and cloud dependencies. The architecture comprises two distinct phases: (1) build-time data processing on a development machine that performs document ingestion, chunking, embedding generation, HNSW indexing, and cross-platform asset distribution; and (2) device runtime execution where the application interface coordinates between the LLM inference engine (utilizing LLaMA.rn with GGUF models) and the RAG processing pipeline (featuring ONNX embeddings, BERT-style tokenization, and HNSW vector search). All runtime components access local storage for models, indices, and document chunks, with performance-critical operations handled through a native bridge. The numbered arrows indicate the complete workflow: (1) asset deployment to device, (2) user query input, (3) query processing through the RAG pipeline, (4) retrieved context injection into the LLM, and (5) generated response delivery.](diagrams/system_architecture.svg)

4.1 Architectural Overview

The architecture comprises six primary components: (1) application interface, (2) Large Language Model (LLM) inference engine, (3) RAG processing pipeline, (4) native bridge, (5) local storage, and (6) data processing pipeline. This design optimizes computational efficiency through native code while maintaining development speed and cross-platform compatibility. The complete RAG workflow includes query embedding with ONNX models, vector retrieval using Rust-based HNSW indexing, prompt augmentation with retrieved contexts, and quantized LLM inference, ensuring data privacy and offline functionality. LLaMA.rn, a React Native binding of llama.cpp, manages the LLM inference operations. The operational flow across these six architectural components proceeds as follows:

4.1.1 Application Interface
This components is composed of a unified mobile interface that is built using React Native. It provides a responsive chat experience with streaming capabilities, on-device model management, and real-time RAG system monitoring. Users can dynamically download quantized GGUF models from Hugging Face repositories optimized for mobile. Key features of the application interface include adaptive document retrieval, displayed performance metrics (TPS, TTFT), and comprehensive debug capabilities, ensuring responsiveness during intensive AI operations via asynchronous processing and optimized threading.

4.1.2 LLM Inference Engine
The LLM inference engine operates independently from the RAG processing pipeline, enabling parallel model loading and execution (See Figure 5). This separation allows the system to maintain LLM state while dynamically incorporating retrieved contexts, supporting hot-swapping between different quantized models without disrupting the embedding pipeline. The architectural isolation ensures memory-intensive LLM operations do not interfere with the lightweight embedding and search processes, though performance degrades as context length increases with additional retrieved documents.


4.1.3 RAG Processing Pipeline

The RAG processing component serves as the intelligent coordinator between user queries and document retrieval, implementing adaptive strategies that optimize performance across different model configurations. Key architectural decisions of the RAG processing pipeline include separating embedding operations from vector search to enable concurrent processing, implementing multi-tiered caching to minimize repeated computations, and developing query complexity analysis to dynamically adjust retrieval parameters. The pipelines's modular design allows independent optimization of tokenization, embedding, and ranking components while maintaining consistent interfaces with both the LLM inference layer and native search operations.

![Figure 2. RAG Processing Pipeline Technical Detail. The pipeline processes user queries through five distinct stages: (1) adaptive preprocessing that determines optimal document retrieval count based on query complexity and the selected model's context window size, (2) dual-tokenizer strategy with local BERT vocabulary or bundled fallback, (3) quantized ONNX embedding generation with attention-weighted pooling, (4) native Rust HNSW vector search with memory-mapped indices achieving sub-5ms performance, and (5) efficient document loading with LRU caching and relevance ranking for context formation.](diagrams/rag.svg)


4.1.4 Native Bridge

The native bridge subsytem addresses the fundamental challenge of deploying high-performance vector search on resource-constrained mobile devices. The architectural approach prioritizes write-once, deploy-everywhere functionality through a unified Rust core with platform-specific integration layers. This design enables consistent search performance across iOS and Android while accommodating each platform's distinct native integration requirements. The offline index construction strategy allows for aggressive optimization during the build phase, trading increased preparation time for superior runtime performance on mobile hardware.

![Figure 3. Native Bridge Architecture for Cross-Platform Rust Integration. The system employs a unified React Native bridge interface that routes to platform-specific implementations: iOS uses direct Objective-C bindings with C header integration (HnswSearch.h) linking to a static Rust library (libhnsw_lib.a), while Android requires an additional JNI layer (hnsw_jni.cpp) compiled into a shared object (libhnsw_native.so) before accessing the same static Rust core. Both platforms converge on identical Rust FFI functions (hnsw_search()) ensuring consistent cross-platform behavior while accommodating platform-specific integration requirements. The architecture demonstrates how a single Rust codebase can provide high-performance native functionality across mobile platforms through appropriate abstraction layers.](diagrams/native_bridge.svg)

4.1.5 Local Storage
The local storage component is where the metadata index, document chunks, and HNSW
index files are housed on the device. The data processing pipeline populates these while the device runtime components consume them. (See Figure 4)

4.6 Data Processing Pipeline

The Python-based offline pipeline prepares datasets from PDFs, Excel files, and plain texts, employing intelligent extraction and semantic-aware chunking strategies. Using identical ONNX embedding models for consistency with runtime operations, the pipeline balances chunk context and retrieval granularity (200-500 tokens per chunk), preserving semantic coherence through chunk overlap. 

![Figure 4. Document Processing and Storage Architecture. The system employs a three-phase approach: (1) offline processing through embed_docs.py that ingests multiple document formats, applies intelligent sentence-aware chunking (1000 characters with 200-character overlap), generates 384-dimensional embeddings using MiniLM-L6-v2, and builds HNSW indices via custom Rust CLI with optimized parameters (M=16, ef\_construction=200); (2) storage architecture featuring chunked metadata system with 1000 documents per 5MB file for mobile memory constraints, memory-mapped hnsw index files (.data/.graph), and automated cross-platform asset distribution; and (3) runtime memory management using LRU cache system (8-chunk maximum, ~40MB) with intelligent O(1) document loading via floor(docID/1000) formula, achieving 85% cache hit rates and 2-5ms load times with automatic prefetching of initial chunks.](diagrams/data_prep_pipeline.svg)

 
4.7 Performance Characteristics and Optimizations

The architecture achieves reasonable performance through optimized memory management, model unloading, and hot-swapping capabilities. The vector search typically achieves sub-second retrieval times with optimized HNSW parameters and efficient memory use, though retrieval performance declines with increased document count. Batch processing optimizes embedding operations, reducing repeated initialization overhead. Quantized LLM inference performance varies significantly with document retrieval volume, typically achieving between 5-20 tokens per second on current mobile hardware, depending on query complexity and retrieval size. 

![Figure 5. Memory Allocation for Parallel LLM and Embedding Model Execution. The system manages concurrent operation of two AI models within mobile memory constraints: the LLM inference engine consumes 70-90% of available memory (1.3-4GB) through quantized GGUF model weights, dynamic context buffers, and generation state, while the embedding model operates efficiently within 5-10% (∼80MB) using a quantized ONNX model, tokenizer, and LRU document cache. Shared resources including memory-mapped HNSW indices and application runtime comprise the remaining allocation. The architecture employs adaptive memory management strategies including dynamic context window scaling and cache eviction to handle memory pressure, enabling practical deployment of edge RAG systems on resource-constrained mobile devices where traditional cloud-based approaches would be prohibitive.](diagrams/memory_management.svg)

4.8 Technical Innovation and Contributions

The SmartVA system integrates high-performance HNSW vector search and optimized ONNX embeddings directly onto resource-constrained mobile hardware. Its multi-model orchestration demonstrates memory and computational management strategies suitable for mobile environments. This modular and scalable design provides insights into privacy-preserving, fully offline AI applications on mobile platforms. 