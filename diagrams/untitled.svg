
<svg width="1000" height="700" viewBox="0 0 1000 700" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1000" height="700" fill="#f8f9fa"/>
  <!-- Title -->
  <text x="500" y="30" text-anchor="middle" font-size="24" font-weight="bold" fill="#1a1a1a">
    Mobile Edge RAG System Architecture
  </text>
  <!-- Device Boundary -->
  <rect x="50" y="60" width="900" height="620" fill="none" stroke="#28a745" stroke-width="3" stroke-dasharray="10,5" rx="15"/>
  <text x="70" y="85" font-size="14" font-weight="bold" fill="#28a745">DEVICE SECURITY BOUNDARY (All Processing On-Device)</text>
  <!-- React Native Layer -->
  <rect x="80" y="100" width="840" height="120" fill="#61dafb" fill-opacity="0.1" stroke="#61dafb" stroke-width="2" rx="8"/>
  <text x="500" y="125" text-anchor="middle" font-size="16" font-weight="bold" fill="#1a1a1a">React Native Application Layer</text>
  <!-- UI Components -->
  <rect x="100" y="140" width="150" height="60" fill="#61dafb" fill-opacity="0.3" stroke="#0056b3" rx="5"/>
  <text x="175" y="165" text-anchor="middle" font-size="12" font-weight="bold">Chat Interface</text>
  <text x="175" y="180" text-anchor="middle" font-size="10">User Input/Output</text>
  <rect x="270" y="140" width="150" height="60" fill="#61dafb" fill-opacity="0.3" stroke="#0056b3" rx="5"/>
  <text x="345" y="165" text-anchor="middle" font-size="12" font-weight="bold">Model Management</text>
  <text x="345" y="180" text-anchor="middle" font-size="10">Download/Load Models</text>
  <rect x="440" y="140" width="150" height="60" fill="#61dafb" fill-opacity="0.3" stroke="#0056b3" rx="5"/>
  <text x="515" y="165" text-anchor="middle" font-size="12" font-weight="bold">RAG Service</text>
  <text x="515" y="180" text-anchor="middle" font-size="10">Query Processing</text>
  <rect x="610" y="140" width="150" height="60" fill="#61dafb" fill-opacity="0.3" stroke="#0056b3" rx="5"/>
  <text x="685" y="165" text-anchor="middle" font-size="12" font-weight="bold">Performance Monitor</text>
  <text x="685" y="180" text-anchor="middle" font-size="10">Metrics Collection</text>
  <rect x="780" y="140" width="120" height="60" fill="#61dafb" fill-opacity="0.3" stroke="#0056b3" rx="5"/>
  <text x="840" y="165" text-anchor="middle" font-size="12" font-weight="bold">Debug Panel</text>
  <text x="840" y="180" text-anchor="middle" font-size="10">Diagnostics</text>
  <!-- LLM Processing Layer -->
  <rect x="80" y="240" width="400" height="180" fill="#ff6b6b" fill-opacity="0.1" stroke="#ff6b6b" stroke-width="2" rx="8"/>
  <text x="280" y="265" text-anchor="middle" font-size="16" font-weight="bold" fill="#1a1a1a">LLM Inference Engine</text>
  <!-- LLaMA Integration -->
  <rect x="100" y="280" width="160" height="80" fill="#ff6b6b" fill-opacity="0.3" stroke="#dc3545" rx="5"/>
  <text x="180" y="305" text-anchor="middle" font-size="12" font-weight="bold">LLaMA.rn</text>
  <text x="180" y="320" text-anchor="middle" font-size="10">GGUF Model</text>
  <text x="180" y="335" text-anchor="middle" font-size="10">Context: 2048-130k</text>
  <text x="180" y="350" text-anchor="middle" font-size="10">GPU Acceleration</text>
  <!-- Context Management -->
  <rect x="280" y="280" width="180" height="80" fill="#ff6b6b" fill-opacity="0.3" stroke="#dc3545" rx="5"/>
  <text x="370" y="305" text-anchor="middle" font-size="12" font-weight="bold">Context Management</text>
  <text x="370" y="320" text-anchor="middle" font-size="10">RAG Context Injection</text>
  <text x="370" y="335" text-anchor="middle" font-size="10">Memory Optimization</text>
  <text x="370" y="350" text-anchor="middle" font-size="10">Token Counting</text>
  <!-- Generation Stats -->
  <rect x="100" y="370" width="360" height="40" fill="#ff6b6b" fill-opacity="0.2" stroke="#dc3545" rx="5"/>
  <text x="280" y="390" text-anchor="middle" font-size="11" font-weight="bold">Performance: TTFT, Tokens/sec, Memory Usage</text>
  <!-- RAG Processing Layer -->
  <rect x="520" y="240" width="400" height="180" fill="#4ecdc4" fill-opacity="0.1" stroke="#4ecdc4" stroke-width="2" rx="8"/>
  <text x="720" y="265" text-anchor="middle" font-size="16" font-weight="bold" fill="#1a1a1a">RAG Processing Pipeline</text>
  <!-- Embedding Model -->
  <rect x="540" y="280" width="120" height="60" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" rx="5"/>
  <text x="600" y="305" text-anchor="middle" font-size="12" font-weight="bold">ONNX Embedding</text>
  <text x="600" y="320" text-anchor="middle" font-size="10">MiniLM-L6-v2</text>
  <text x="600" y="330" text-anchor="middle" font-size="10">384-dim vectors</text>
  <!-- Tokenizer -->
  <rect x="680" y="280" width="120" height="60" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" rx="5"/>
  <text x="740" y="305" text-anchor="middle" font-size="12" font-weight="bold">Tokenizer</text>
  <text x="740" y="320" text-anchor="middle" font-size="10">Local/Bundled</text>
  <text x="740" y="330" text-anchor="middle" font-size="10">BERT-style</text>
  <!-- HNSW Search -->
  <rect x="820" y="280" width="80" height="60" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" rx="5"/>
  <text x="860" y="300" text-anchor="middle" font-size="11" font-weight="bold">HNSW</text>
  <text x="860" y="315" text-anchor="middle" font-size="10">Rust Native</text>
  <text x="860" y="330" text-anchor="middle" font-size="10">Fast Search</text>
  <!-- Document Management -->
  <rect x="540" y="350" width="360" height="60" fill="#4ecdc4" fill-opacity="0.2" stroke="#17a2b8" rx="5"/>
  <text x="720" y="375" text-anchor="middle" font-size="12" font-weight="bold">Document Management</text>
  <text x="650" y="390" text-anchor="middle" font-size="10">Chunked Metadata</text>
  <text x="790" y="390" text-anchor="middle" font-size="10">LRU Cache (8 chunks)</text>
  <!-- Storage Layer -->
  <rect x="80" y="440" width="840" height="120" fill="#ffd93d" fill-opacity="0.1" stroke="#ffd93d" stroke-width="2" rx="8"/>
  <text x="500" y="465" text-anchor="middle" font-size="16" font-weight="bold" fill="#1a1a1a">Local Storage Layer</text>
  <!-- Document Store -->
  <rect x="100" y="480" width="140" height="60" fill="#ffd93d" fill-opacity="0.3" stroke="#f39c12" rx="5"/>
  <text x="170" y="505" text-anchor="middle" font-size="12" font-weight="bold">Document Store</text>
  <text x="170" y="520" text-anchor="middle" font-size="10">JSON chunks</text>
  <text x="170" y="530" text-anchor="middle" font-size="10">1000 docs/file</text>
  <!-- HNSW Index -->
  <rect x="260" y="480" width="140" height="60" fill="#ffd93d" fill-opacity="0.3" stroke="#f39c12" rx="5"/>
  <text x="330" y="500" text-anchor="middle" font-size="12" font-weight="bold">HNSW Index</text>
  <text x="330" y="515" text-anchor="middle" font-size="10">.data/.graph files</text>
  <text x="330" y="530" text-anchor="middle" font-size="10">Memory-mapped</text>
  <!-- Models -->
  <rect x="420" y="480" width="140" height="60" fill="#ffd93d" fill-opacity="0.3" stroke="#f39c12" rx="5"/>
  <text x="490" y="500" text-anchor="middle" font-size="12" font-weight="bold">AI Models</text>
  <text x="490" y="515" text-anchor="middle" font-size="10">GGUF + ONNX</text>
  <text x="490" y="530" text-anchor="middle" font-size="10">Downloaded</text>
  <!-- Cache -->
  <rect x="580" y="480" width="140" height="60" fill="#ffd93d" fill-opacity="0.3" stroke="#f39c12" rx="5"/>
  <text x="650" y="500" text-anchor="middle" font-size="12" font-weight="bold">Runtime Cache</text>
  <text x="650" y="515" text-anchor="middle" font-size="10">Metadata chunks</text>
  <text x="650" y="530" text-anchor="middle" font-size="10">LRU eviction</text>
  <!-- Assets -->
  <rect x="740" y="480" width="160" height="60" fill="#ffd93d" fill-opacity="0.3" stroke="#f39c12" rx="5"/>
  <text x="820" y="500" text-anchor="middle" font-size="12" font-weight="bold">Bundled Assets</text>
  <text x="820" y="515" text-anchor="middle" font-size="10">Initial index/vocab</text>
  <text x="820" y="530" text-anchor="middle" font-size="10">iOS/Android assets</text>
  <!-- Native Bridge Layer -->
  <rect x="80" y="580" width="840" height="80" fill="#95a5a6" fill-opacity="0.1" stroke="#95a5a6" stroke-width="2" rx="8"/>
  <text x="500" y="605" text-anchor="middle" font-size="16" font-weight="bold" fill="#1a1a1a">Native Bridge Layer</text>
  <!-- Rust HNSW Module -->
  <rect x="100" y="620" width="200" height="30" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" rx="5"/>
  <text x="200" y="640" text-anchor="middle" font-size="12" font-weight="bold">Rust HNSW Module (lib.rs)</text>
  <!-- Platform Bridges -->
  <rect x="320" y="620" width="120" height="30" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" rx="5"/>
  <text x="380" y="640" text-anchor="middle" font-size="12" font-weight="bold">iOS Bridge</text>
  <rect x="460" y="620" width="120" height="30" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" rx="5"/>
  <text x="520" y="640" text-anchor="middle" font-size="12" font-weight="bold">Android Bridge</text>
  <!-- ONNX Runtime -->
  <rect x="600" y="620" width="120" height="30" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" rx="5"/>
  <text x="660" y="640" text-anchor="middle" font-size="12" font-weight="bold">ONNX Runtime</text>
  <!-- File System -->
  <rect x="740" y="620" width="160" height="30" fill="#95a5a6" fill-opacity="0.3" stroke="#6c757d" rx="5"/>
  <text x="820" y="640" text-anchor="middle" font-size="12" font-weight="bold">React Native FS</text>
  <!-- Data Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- User Query Flow -->
  <path d="M 175 200 L 175 280" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="185" y="240" font-size="10" fill="#333">User Query</text>
  <!-- RAG Flow -->
  <path d="M 515 200 L 600 280" stroke="#007bff" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="550" y="240" font-size="10" fill="#007bff">Embed Query</text>
  <!-- Search Flow -->
  <path d="M 800 320 L 860 320" stroke="#007bff" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Context Injection -->
  <path d="M 460 320 L 280 320" stroke="#dc3545" stroke-width="2" marker-end="url(#arrowhead)"/>
  <text x="350" y="310" font-size="10" fill="#dc3545">RAG Context</text>
  <!-- Storage Access -->
  <path d="M 600 340 L 600 480" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 860 340 L 330 480" stroke="#f39c12" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Native Calls -->
  <path d="M 860 340 L 200 620" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 600 340 L 660 620" stroke="#6c757d" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Security Benefits Box -->
  <rect x="960" y="100" width="35" height="500" fill="#28a745" fill-opacity="0.1" stroke="#28a745" stroke-width="2" rx="5"/>
  <text x="977" y="130" text-anchor="middle" font-size="10" font-weight="bold" fill="#28a745" transform="rotate(90 977 130)">SECURITY BENEFITS</text>
  <text x="977" y="200" text-anchor="middle" font-size="8" fill="#28a745" transform="rotate(90 977 200)">• No Data Leaves Device</text>
  <text x="977" y="280" text-anchor="middle" font-size="8" fill="#28a745" transform="rotate(90 977 280)">• No API Keys Required</text>
  <text x="977" y="360" text-anchor="middle" font-size="8" fill="#28a745" transform="rotate(90 977 360)">• No Network Dependencies</text>
  <text x="977" y="440" text-anchor="middle" font-size="8" fill="#28a745" transform="rotate(90 977 440)">• Complete User Control</text>
  <text x="977" y="520" text-anchor="middle" font-size="8" fill="#28a745" transform="rotate(90 977 520)">• Offline Operation</text>
</svg>