``` mermaid
graph TB
    %% React Native Layer
    subgraph "📱 React Native App"
        UI[Chat UI & Controls]
        RAG[RAG Service]
    end

    %% Native Performance Layer
    subgraph "⚡ Native Bridges"
        Bridge[Native Bridge Layer]
    end

    %% Native Modules
    subgraph "📦 Platform Modules"
        iOS[iOS Modules & Assets]
        Android[Android Modules & Assets]
    end

    %% Rust Core
    subgraph "🛠️ Rust Core Engine"
        Rust[Vector Search Engine]
    end

    %% Simplified connections
    UI --> RAG
    RAG --> Bridge
    Bridge --> iOS
    Bridge --> Android
    iOS --> Rust
    Android --> Rust

    %% Styling
    classDef mobile fill:#61dafb,stroke:#20232a,stroke-width:2px
    classDef native fill:#ffd54f,stroke:#f57f17,stroke-width:2px
    classDef platform fill:#b2dfdb,stroke:#00695c,stroke-width:2px
    classDef rust fill:#ff8a80,stroke:#b71c1c,stroke-width:2px

    class UI,RAG mobile
    class Bridge native
    class iOS,Android platform
    class Rust rust
```