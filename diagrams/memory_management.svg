<svg width="1020" height="630" viewBox="0 0 1020 630" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="1020" height="630" fill="#FFFFFF"/>
  <!-- Mobile Device Memory Container -->
  <rect x="50" y="50" width="920" height="530" fill="none" stroke="#333" stroke-width="3" rx="15"/>
  <text x="70" y="80" font-size="22" font-weight="bold">MOBILE DEVICE MEMORY (~1-4GB Available to App)</text>
  <!-- LLM Memory Block -->
  <rect x="80" y="100" width="380" height="380" fill="#ff6b6b" fill-opacity="0.15" stroke="#dc3545" stroke-width="2" rx="10"/>
  <text x="270" y="130" text-anchor="middle" font-size="20" font-weight="bold">LLM INFERENCE ENGINE</text>
  <!-- Model Weights -->
  <rect x="100" y="150" width="340" height="90" fill="#ff6b6b" fill-opacity="0.4" stroke="#dc3545" stroke-width="2" rx="8"/>
  <text x="270" y="175" text-anchor="middle" font-size="18" font-weight="bold">Model Weights (GGUF)</text>
  <text x="180" y="200" text-anchor="middle" font-size="16">1B Model: ~1.2GB</text>
  <text x="360" y="200" text-anchor="middle" font-size="16">3B Model: ~3.5GB</text>
  <text x="270" y="220" text-anchor="middle" font-size="16">Quantized • Read-only • Memory-mapped</text>
  <!-- Context Buffer -->
  <rect x="100" y="260" width="160" height="80" fill="#ff6b6b" fill-opacity="0.3" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="180" y="280" text-anchor="middle" font-size="16" font-weight="bold">Context Buffer</text>
  <text x="180" y="300" text-anchor="middle" font-size="14">2k tokens: ~16MB</text>
  <text x="180" y="315" text-anchor="middle" font-size="14">32k tokens: ~256MB</text>
  <text x="180" y="330" text-anchor="middle" font-size="14">Dynamic allocation</text>
  <!-- KV Cache -->
  <rect x="280" y="260" width="160" height="80" fill="#ff6b6b" fill-opacity="0.3" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="360" y="280" text-anchor="middle" font-size="16" font-weight="bold">KV Cache</text>
  <text x="360" y="300" text-anchor="middle" font-size="14">Attention states</text>
  <text x="360" y="315" text-anchor="middle" font-size="14">~50-200MB</text>
  <text x="360" y="330" text-anchor="middle" font-size="14">Generation state</text>
  <!-- Generation Buffers -->
  <rect x="100" y="360" width="340" height="60" fill="#ff6b6b" fill-opacity="0.2" stroke="#dc3545" stroke-width="1" rx="8"/>
  <text x="270" y="385" text-anchor="middle" font-size="16" font-weight="bold">Generation Buffers</text>
  <text x="270" y="405" text-anchor="middle" font-size="14">Token processing • Temporary arrays • ~20-50MB</text>
  <!-- Total LLM Memory -->
  <rect x="100" y="440" width="340" height="30" fill="#dc3545" fill-opacity="0.6" stroke="#8B0000" stroke-width="2" rx="5"/>
  <text x="270" y="460" text-anchor="middle" font-size="16" font-weight="bold">Total: 1.3-4GB (70-90% of available memory)</text>
  <!-- Embedding Model Memory Block -->
  <rect x="500" y="150" width="280" height="330" fill="#4ecdc4" fill-opacity="0.15" stroke="#17a2b8" stroke-width="2" rx="10"/>
  <text x="640" y="180" text-anchor="middle" font-size="20" font-weight="bold">EMBEDDING MODEL</text>
  <!-- ONNX Model -->
  <rect x="520" y="200" width="240" height="60" fill="#4ecdc4" fill-opacity="0.4" stroke="#17a2b8" stroke-width="2" rx="8"/>
  <text x="640" y="225" text-anchor="middle" font-size="18" font-weight="bold">ONNX Model Weights</text>
  <text x="640" y="245" text-anchor="middle" font-size="16">MiniLM-L6-v2 quantized: ~25MB</text>
  <!-- Tokenizer -->
  <rect x="520" y="280" width="110" height="50" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="575" y="300" text-anchor="middle" font-size="16" font-weight="bold">Tokenizer</text>
  <text x="575" y="320" text-anchor="middle" font-size="14">Vocab: ~5MB</text>
  <!-- Runtime Buffers -->
  <rect x="650" y="280" width="110" height="50" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="705" y="300" text-anchor="middle" font-size="16" font-weight="bold">Runtime</text>
  <text x="705" y="320" text-anchor="middle" font-size="14">Buffers: ~10MB</text>
  <!-- Document Cache -->
  <rect x="520" y="350" width="240" height="60" fill="#4ecdc4" fill-opacity="0.3" stroke="#17a2b8" stroke-width="1" rx="8"/>
  <text x="640" y="375" text-anchor="middle" font-size="16" font-weight="bold">Document Cache (LRU)</text>
  <text x="640" y="395" text-anchor="middle" font-size="14">8 chunks × 5MB = ~40MB maximum</text>
  <!-- Total Embedding Memory -->
  <rect x="520" y="430" width="240" height="30" fill="#17a2b8" fill-opacity="0.6" stroke="#006064" stroke-width="2" rx="5"/>
  <text x="640" y="450" text-anchor="middle" font-size="16" font-weight="bold">Total: ~80MB (5-10% of memory)</text>
  <!-- Shared Resources -->
  <rect x="800" y="100" width="140" height="280" fill="#ffd93d" fill-opacity="0.2" stroke="#f39c12" stroke-width="2" rx="10"/>
  <text x="870" y="130" text-anchor="middle" font-size="20" font-weight="bold">SHARED</text>
  <text x="870" y="150" text-anchor="middle" font-size="20" font-weight="bold">RESOURCES</text>
  <!-- HNSW Index -->
  <rect x="810" y="170" width="120" height="60" fill="#ffd93d" fill-opacity="0.4" stroke="#f39c12" stroke-width="1" rx="8"/>
  <text x="870" y="190" text-anchor="middle" font-size="16" font-weight="bold">HNSW Index</text>
  <text x="870" y="205" text-anchor="middle" font-size="14">Memory-mapped</text>
  <text x="870" y="220" text-anchor="middle" font-size="14">~10-50MB</text>
  <!-- File Buffers -->
  <rect x="810" y="240" width="120" height="60" fill="#ffd93d" fill-opacity="0.4" stroke="#f39c12" stroke-width="1" rx="8"/>
  <text x="870" y="260" text-anchor="middle" font-size="16" font-weight="bold">File I/O</text>
  <text x="870" y="275" text-anchor="middle" font-size="14">OS buffers</text>
  <text x="870" y="290" text-anchor="middle" font-size="14">~20-30MB</text>
  <!-- App Runtime -->
  <rect x="810" y="310" width="120" height="60" fill="#ffd93d" fill-opacity="0.4" stroke="#f39c12" stroke-width="1" rx="8"/>
  <text x="870" y="330" text-anchor="middle" font-size="16" font-weight="bold">App Runtime</text>
  <text x="870" y="345" text-anchor="middle" font-size="14">React Native</text>
  <text x="870" y="360" text-anchor="middle" font-size="14">~100-200MB</text>
  <!-- Memory Management Strategy -->
  <rect x="80" y="500" width="860" height="60" fill="#CFB8E4" fill-opacity="0.2" stroke="#4B0092" stroke-width="2" rx="10"/>
  <text x="510" y="525" text-anchor="middle" font-size="20" font-weight="bold">MEMORY PRESSURE STRATEGY</text>
  <text x="510" y="550" text-anchor="middle" font-size="16">LLM: Reduce context window • RAG: Clear document cache • Critical: Fallback to smaller model</text>
  <!-- Memory Isolation Indicators -->
  <path d="M 480 250 L 480 250" stroke="none"/>
  <text x="480" y="250" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="270" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="290" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="310" font-size="16" font-weight="bold" fill="#666">|</text>
  <text x="480" y="330" font-size="16" font-weight="bold" fill="#666">|</text>
  <!-- Memory Flow Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- Shared resource arrows -->
  <path d="M 780 175 L 800 175" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 460 125 L 800 125" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
</svg>