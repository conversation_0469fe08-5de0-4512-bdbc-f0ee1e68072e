``` mermaid
flowchart LR
    Query[User Query]

    Embedding[Embedding]
    Search[Vector Search]
    Retrieval[Retrieve Docs]
    Ranking[Ranking]
    Context[Context Preparation]

    Prompt[LLM Prompt]
    Generation[LLM Generation]
    Response[Response]

    VectorDB[(Embeddings DB)]
    MetaDB[(Metadata DB)]

    %% Flow
    Query --> Embedding --> Search --> VectorDB --> Retrieval --> MetaDB --> Ranking --> Context
    Context --> Prompt --> Generation --> Response

    %% Improved High-Contrast Styling
    classDef process fill:#e8f5e8,stroke:#388e3c,color:#000,font-size:13px
    classDef data fill:#e3f2fd,stroke:#1565c0,color:#000,font-size:13px
    classDef llm fill:#fff3e0,stroke:#ef6c00,color:#000,font-size:13px
    classDef user fill:#f3e5f5,stroke:#7b1fa2,color:#000,font-size:13px

    class Query,Response user
    class Embedding,Search,Retrieval,Ranking,Context process
    class Prompt,Generation llm
    class VectorDB,MetaDB data
```