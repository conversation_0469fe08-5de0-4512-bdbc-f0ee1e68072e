
``` mermaid
graph LR
    %% High-level components only
    subgraph "📱 Mobile Application"
        UI[React Native UI]
    end

    subgraph "🧠 AI Processing"
        LLM[LLaMA.rn]
        RAG[RAG Retrieval]
    end

    subgraph "⚡ Native Performance"
        HNSW[HNSW Vector Search]
        ONNX[ONNX Embeddings]
    end

    subgraph "💾 Data Layer"
        Models[LLM Models]
        Index[Vector Index]
        Docs[Knowledge Base]
    end

    subgraph "🔧 Build Pipeline"
        Python[Doc Processing Scripts]
    end

    %% Connections
    UI --> LLM
    UI --> RAG

    LLM --> Models
    RAG --> HNSW
    RAG --> ONNX

    HNSW --> Index
    ONNX --> Index

    Python --> Index
    Python --> Docs
    Python --> Models

    %% High-contrast Styling
    classDef mobile fill:#e3f2fd,stroke:#1976d2,stroke-width:2px,color:#000,font-size:13px
    classDef ai fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000,font-size:13px
    classDef native fill:#fff3e0,stroke:#f57c00,stroke-width:2px,color:#000,font-size:13px
    classDef data fill:#f0f0f0,stroke:#616161,stroke-width:2px,color:#000,font-size:13px
    classDef build fill:#e0f2f1,stroke:#00695c,stroke-width:2px,color:#000,font-size:13px

    class UI mobile
    class LLM,RAG ai
    class HNSW,ONNX native
    class Models,Index,Docs data
    class Python build
```