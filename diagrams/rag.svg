<svg width="940" height="730" viewBox="0 0 940 730" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="940" height="730" fill="#FFFFFF"/>
  <!-- Stage 1: Query Input -->
  <rect x="50" y="60" width="840" height="80" fill="#e3f2fd" stroke="#1976d2" stroke-width="2" rx="8"/>
  <text x="470" y="85" text-anchor="middle" font-size="18" font-weight="bold">Stage 1: Query Input  Preprocessing</text>
  <rect x="70" y="100" width="150" height="30" fill="#bbdefb" stroke="#1976d2" rx="5"/>
  <text x="145" y="120" text-anchor="middle" font-size="14" font-weight="bold">User Query</text>
  <rect x="240" y="100" width="150" height="30" fill="#bbdefb" stroke="#1976d2" rx="5"/>
  <text x="315" y="120" text-anchor="middle" font-size="14" font-weight="bold">Text Cleaning</text>
  <rect x="410" y="100" width="150" height="30" fill="#bbdefb" stroke="#1976d2" rx="5"/>
  <text x="485" y="120" text-anchor="middle" font-size="14" font-weight="bold">Complexity Analysis</text>
  <rect x="580" y="100" width="150" height="30" fill="#bbdefb" stroke="#1976d2" rx="5"/>
  <text x="655" y="120" text-anchor="middle" font-size="14" font-weight="bold">Adaptive Chunking</text>
  <rect x="750" y="100" width="120" height="30" fill="#bbdefb" stroke="#1976d2" rx="5"/>
  <text x="810" y="120" text-anchor="middle" font-size="14" font-weight="bold">k = 3-25 docs</text>
  <!-- Stage 2: Tokenization -->
  <rect x="50" y="170" width="840" height="100" fill="#e8f5e8" stroke="#388e3c" stroke-width="2" rx="8"/>
  <text x="470" y="195" text-anchor="middle" font-size="18" font-weight="bold" fill="#1a1a1a">Stage 2: Tokenization</text>
  <!-- Tokenizer Options -->
  <rect x="70" y="215" width="180" height="45" fill="#c8e6c9" stroke="#388e3c" rx="5"/>
  <text x="160" y="235" text-anchor="middle" font-size="14" font-weight="bold">Local Tokenizer</text>
  <text x="160" y="250" text-anchor="middle" font-size="12">vocab.json + exact BERT</text>
<text x="270" y="240" text-anchor="middle" font-size="14" font-weight="bold" fill="#666">OR</text>
  <rect x="290" y="215" width="180" height="45" fill="#c8e6c9" stroke="#388e3c" rx="5"/>
  <text x="380" y="235" text-anchor="middle" font-size="14" font-weight="bold">Bundled Tokenizer</text>
  <text x="380" y="250" text-anchor="middle" font-size="12">Fallback with core vocab</text>
  <!-- Tokenization Output -->
  <rect x="510" y="215" width="160" height="45" fill="#a5d6a7" stroke="#388e3c" rx="5"/>
  <text x="590" y="235" text-anchor="middle" font-size="14" font-weight="bold">Token IDs</text>
  <text x="590" y="250" text-anchor="middle" font-size="12">[101, 7592, 2054, ...102]</text>
  <rect x="710" y="215" width="160" height="45" fill="#a5d6a7" stroke="#388e3c" rx="5"/>
  <text x="790" y="235" text-anchor="middle" font-size="14" font-weight="bold">Attention Mask</text>
  <text x="790" y="250" text-anchor="middle" font-size="12">[1, 1, 1, ...0, 0, 0]</text>
  <!-- Stage 3: Embedding Generation -->
  <rect x="50" y="300" width="840" height="120" fill="#fff3e0" stroke="#f57c00" stroke-width="2" rx="8"/>
  <text x="470" y="325" text-anchor="middle" font-size="18" font-weight="bold" fill="#1a1a1a">Stage 3: Embedding Generation</text>
  <!-- ONNX Model -->
  <rect x="70" y="345" width="200" height="60" fill="#ffcc02" stroke="#f57c00" rx="5"/>
  <text x="170" y="365" text-anchor="middle" font-size="14" font-weight="bold">ONNX Runtime</text>
  <text x="170" y="380" text-anchor="middle" font-size="12">all-MiniLM-L6-v2.quant</text>
  <text x="170" y="395" text-anchor="middle" font-size="12">Quantized for mobile</text>
  <!-- Processing Steps -->
  <rect x="290" y="345" width="140" height="60" fill="#ffe0b2" stroke="#f57c00" rx="5"/>
  <text x="360" y="365" text-anchor="middle" font-size="14" font-weight="bold">Forward Pass</text>
  <text x="360" y="380" text-anchor="middle" font-size="12">input_ids →</text>
  <text x="360" y="395" text-anchor="middle" font-size="12">hidden_states</text>
  <rect x="450" y="345" width="140" height="60" fill="#ffe0b2" stroke="#f57c00" rx="5"/>
  <text x="520" y="365" text-anchor="middle" font-size="14" font-weight="bold">Mean Pooling</text>
  <text x="520" y="380" text-anchor="middle" font-size="12">Attention-weighted</text>
  <text x="520" y="395" text-anchor="middle" font-size="12">sequence average</text>
  <rect x="610" y="345" width="140" height="60" fill="#ffe0b2" stroke="#f57c00" rx="5"/>
  <text x="680" y="365" text-anchor="middle" font-size="14" font-weight="bold">Normalization</text>
  <text x="680" y="380" text-anchor="middle" font-size="12">L2 normalize</text>
  <text x="680" y="395" text-anchor="middle" font-size="12">→ unit vector</text>
  <rect x="770" y="345" width="100" height="60" fill="#ffb74d" stroke="#f57c00" rx="5"/>
  <text x="820" y="365" text-anchor="middle" font-size="14" font-weight="bold">384-dim</text>
  <text x="820" y="380" text-anchor="middle" font-size="14" font-weight="bold">Vector</text>
  <text x="820" y="395" text-anchor="middle" font-size="12">Float32[]</text>
  <!-- Stage 4: Vector Search -->
  <rect x="50" y="450" width="840" height="120" fill="#fce4ec" stroke="#c2185b" stroke-width="2" rx="8"/>
  <text x="470" y="475" text-anchor="middle" font-size="18" font-weight="bold" fill="#1a1a1a">Stage 4: HNSW Vector Search</text>
  <!-- Search Process -->
  <rect x="70" y="495" width="140" height="60" fill="#f8bbd9" stroke="#c2185b" rx="5"/>
  <text x="140" y="515" text-anchor="middle" font-size="14" font-weight="bold">Native Bridge</text>
  <text x="140" y="530" text-anchor="middle" font-size="12">React Native →</text>
  <text x="140" y="545" text-anchor="middle" font-size="12">Rust lib.rs</text>
  <rect x="235" y="495" width="140" height="60" fill="#f8bbd9" stroke="#c2185b" rx="5"/>
  <text x="305" y="515" text-anchor="middle" font-size="14" font-weight="bold">Index Loading</text>
  <text x="305" y="530" text-anchor="middle" font-size="12">Memory-mapped</text>
  <text x="305" y="545" text-anchor="middle" font-size="12">.data + .graph</text>
  <rect x="400" y="495" width="140" height="60" fill="#f8bbd9" stroke="#c2185b" rx="5"/>
  <text x="470" y="515" text-anchor="middle" font-size="14" font-weight="bold">Cosine Search</text>
  <text x="470" y="530" text-anchor="middle" font-size="12">HNSW traversal</text>
  <text x="470" y="545" text-anchor="middle" font-size="12">max(k, ef_construction)</text>
  <rect x="565" y="495" width="140" height="60" fill="#f8bbd9" stroke="#c2185b" rx="5"/>
  <text x="635" y="515" text-anchor="middle" font-size="14" font-weight="bold">Top-k Results</text>
  <text x="635" y="530" text-anchor="middle" font-size="12">Document IDs +</text>
  <text x="635" y="545" text-anchor="middle" font-size="12">Distance scores</text>
  <rect x="730" y="495" width="140" height="60" fill="#f48fb1" stroke="#c2185b" rx="5"/>
  <text x="800" y="515" text-anchor="middle" font-size="14" font-weight="bold">Performance</text>
  <text x="800" y="530" text-anchor="middle" font-size="12">~1-5ms search</text>
  <text x="800" y="545" text-anchor="middle" font-size="12">O(log n) complexity</text>
  <!-- Stage 5: Document Retrieval -->
  <rect x="50" y="600" width="840" height="80" fill="#f3e5f5" stroke="#7b1fa2" stroke-width="2" rx="8"/>
  <text x="470" y="625" text-anchor="middle" font-size="18" font-weight="bold">Stage 5: Document Loading  Context Formation</text>
  <rect x="70" y="640" width="120" height="30" fill="#e1bee7" stroke="#7b1fa2" rx="3"/>
  <text x="130" y="660" text-anchor="middle" font-size="14" font-weight="bold">Batch Load</text>
  <rect x="214" y="640" width="120" height="30" fill="#e1bee7" stroke="#7b1fa2" rx="3"/>
  <text x="274" y="660" text-anchor="middle" font-size="14" font-weight="bold">LRU Cache</text>
  <rect x="358" y="640" width="120" height="30" fill="#e1bee7" stroke="#7b1fa2" rx="3"/>
  <text x="418" y="660" text-anchor="middle" font-size="14" font-weight="bold">Metadata + Docs</text>
  <rect x="502" y="640" width="120" height="30" fill="#e1bee7" stroke="#7b1fa2" rx="3"/>
  <text x="562" y="660" text-anchor="middle" font-size="14" font-weight="bold">Ranking</text>
  <rect x="646" y="640" width="120" height="30" fill="#e1bee7" stroke="#7b1fa2" rx="3"/>
  <text x="706" y="660" text-anchor="middle" font-size="14" font-weight="bold">Formatting</text>
  <rect x="790" y="640" width="80" height="30" fill="#ce93d8" stroke="#7b1fa2" rx="3"/>
  <text x="830" y="660" text-anchor="middle" font-size="14" font-weight="bold">→ LLM</text>
  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- Flow arrows between stages -->
  <path d="M 470 140 L 470 170" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 470 270 L 470 300" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 470 420 L 470 450" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 470 570 L 470 600" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Internal flow arrows -->
  <!-- Stage 1: Query Input -->
  <path d="M 220 115 L 240 115" stroke="#1976d2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 390 115 L 410 115" stroke="#1976d2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 560 115 L 580 115" stroke="#1976d2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 730 115 L 750 115" stroke="#1976d2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <!-- Stage 2: Tokenization -->
  <path d="M 470 238 L 510 238" stroke="#388e3c" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 670 238 L 710 238" stroke="#388e3c" stroke-width="1" marker-end="url(#arrowhead)"/>
  <!-- Stage 3: Embedding Generation -->
  <path d="M 270 375 L 290 375" stroke="#f57c00" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 430 375 L 450 375" stroke="#f57c00" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 590 375 L 610 375" stroke="#f57c00" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 750 375 L 770 375" stroke="#f57c00" stroke-width="1" marker-end="url(#arrowhead)"/>
  <!-- Stage 4: Vector Search -->
  <path d="M 210 525 L 235 525" stroke="#c2185b" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 375 525 L 400 525" stroke="#c2185b" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 540 525 L 565 525" stroke="#c2185b" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 705 525 L 730 525" stroke="#c2185b" stroke-width="1" marker-end="url(#arrowhead)"/>
  <!-- Stage 5: Document Retrieval -->
  <path d="M 190 655 L 214 655" stroke="#7b1fa2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 334 655 L 358 655" stroke="#7b1fa2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 478 655 L 502 655" stroke="#7b1fa2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 622 655 L 646 655" stroke="#7b1fa2" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 766 655 L 790 655" stroke="#7b1fa2" stroke-width="1" marker-end="url(#arrowhead)"/>
</svg>