``` mermaid
graph TD
    %% User Interaction
    subgraph User Input
        User[User Query]
    end

    %% Embedding
    subgraph Embedding Layer
        Tokenizer[Tokenizer]
        Embedding[ONNX Embeddings]
    end

    %% Vector Search
    subgraph Retrieval Layer
        HNSW[HNSW Search]
        VectorDB[(Embeddings DB)]
        TopK[Top-K Docs]
    end

    %% Context Assembly
    subgraph Context Layer
        Retrieval[Doc Retrieval]
        Ranking[Ranking]
        Context[Context Preparation]
    end

    %% LLM Generation
    subgraph LLM Layer
        LLM[Quantized LLM]
        Generation[Generation]
    end

    %% Output
    subgraph Output
        Response[Final Response]
    end

    %% Vertical flow between layers
    User --> Tokenizer --> Embedding
    Embedding --> HNSW --> TopK
    HNSW --> VectorDB
    TopK --> Retrieval --> Ranking --> Context
    Context --> LLM --> Generation --> Response

    %% High-contrast Styling
    classDef user fill:#e8eaf6,stroke:#3949ab,color:#000,font-size:13px
    classDef embed fill:#e3f2fd,stroke:#1565c0,color:#000,font-size:13px
    classDef retrieval fill:#f1f8e9,stroke:#7cb342,color:#000,font-size:13px
    classDef context fill:#fff3e0,stroke:#f57c00,color:#000,font-size:13px
    classDef llm fill:#ffebee,stroke:#d32f2f,color:#000,font-size:13px
    classDef output fill:#e0f2f1,stroke:#00897b,color:#000,font-size:13px

    class User user
    class Tokenizer,Embedding embed
    class HNSW,VectorDB,TopK retrieval
    class Retrieval,Ranking,Context context
    class LLM,Generation llm
    class Response output
```