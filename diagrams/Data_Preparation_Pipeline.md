``` mermaid
flowchart TD
    %% Inputs
    PDFs[PDFs]
    Excel[Excel]
    Text[Text Files]

    %% Process
    Extract[Extraction]
    Chunk[Chunking]
    Embed[Embedding]
    Index[HNSW Indexing]
    Package[Mobile Packaging]

    %% Outputs
    VectorIndex[(Vector Index)]
    Metadata[(Metadata)]
    EmbedModel[(ONNX Model)]

    %% Mobile
    iOS[iOS Bundle]
    Android[Android Bundle]

    %% Flow
    PDFs --> Extract
    Excel --> Extract
    Text --> Extract

    Extract --> Chunk --> Embed --> Index --> Package
    Index --> VectorIndex
    Chunk --> Metadata
    Embed --> EmbedModel
    Package --> iOS
    Package --> Android

    %% High-contrast Styling
    classDef input fill:#e8f5e8,stroke:#388e3c,color:#000,font-size:13px
    classDef process fill:#e3f2fd,stroke:#1565c0,color:#000,font-size:13px
    classDef output fill:#fff3e0,stroke:#ef6c00,color:#000,font-size:13px
    classDef mobile fill:#f3e5f5,stroke:#7b1fa2,color:#000,font-size:13px

    class PDFs,Excel,Text input
    class Extract,Chunk,Embed,Index,Package process
    class VectorIndex,Metadata,EmbedModel output
    class iOS,Android mobile
```