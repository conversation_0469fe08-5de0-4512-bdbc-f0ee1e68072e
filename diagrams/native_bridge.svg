
<svg width="520" height="640" viewBox="0 0 520 640" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="520" height="640" fill="#FFFFFF"/>
  <!-- React Native Bridge Layer -->
  <rect x="110" y="50" width="300" height="100" fill="#95a5a6" fill-opacity="0.2" stroke="#6c757d" stroke-width="2" rx="10"/>
  <text x="260" y="78" text-anchor="middle" font-size="22" font-weight="bold">REACT NATIVE BRIDGE</text>
  <!-- Bridge Interface -->
  <rect x="160" y="90" width="200" height="50" fill="#95a5a6" fill-opacity="0.4" stroke="#6c757d" stroke-width="2" rx="8"/>
  <text x="260" y="115" text-anchor="middle" font-size="16" font-weight="bold">HnswSearchModule</text>
  <text x="260" y="130" text-anchor="middle" font-size="13">searchKnn() Interface</text>
  <!-- iOS Platform -->
  <rect x="50" y="180" width="200" height="300" fill="#007aff" fill-opacity="0.1" stroke="#0051d5" stroke-width="2" rx="10"/>
  <text x="150" y="210" text-anchor="middle" font-size="22" font-weight="bold">iOS</text>
  <text x="150" y="235" text-anchor="middle" font-size="22" font-weight="bold">NATIVE</text>
  <!-- iOS Bridge -->
  <rect x="75" y="245" width="150" height="60" fill="#007aff" fill-opacity="0.3" stroke="#0051d5" stroke-width="1" rx="8"/>
  <text x="150" y="265" text-anchor="middle" font-size="16" font-weight="bold">iOS Bridge</text>
  <text x="150" y="280" text-anchor="middle" font-size="14">Objective-C</text>
  <text x="150" y="295" text-anchor="middle" font-size="14">RCTBridgeModule</text>
  <!-- iOS C Header -->
  <rect x="75" y="330" width="150" height="60" fill="#007aff" fill-opacity="0.3" stroke="#0051d5" stroke-width="1" rx="8"/>
  <text x="150" y="350" text-anchor="middle" font-size="16" font-weight="bold">C Header Bridge</text>
  <text x="150" y="365" text-anchor="middle" font-size="14">HnswSearch.h</text>
  <text x="150" y="380" text-anchor="middle" font-size="14">extern "C" FFI</text>
  <!-- iOS Static Library -->
  <rect x="75" y="415" width="150" height="50" fill="#007aff" fill-opacity="0.2" stroke="#0051d5" stroke-width="1" rx="8"/>
  <text x="150" y="438" text-anchor="middle" font-size="16" font-weight="bold">Static Rust Library</text>
  <text x="150" y="453" text-anchor="middle" font-size="14">libhnsw_lib.a</text>
  <!-- Android Platform -->
  <rect x="270" y="180" width="200" height="300" fill="#3ddc84" fill-opacity="0.1" stroke="#00701a" stroke-width="2" rx="10"/>
  <text x="370" y="210" text-anchor="middle" font-size="22" font-weight="bold">ANDROID</text>
  <text x="370" y="235" text-anchor="middle" font-size="22" font-weight="bold">NATIVE</text>
  <!-- Android Bridge -->
  <rect x="295" y="245" width="150" height="60" fill="#3ddc84" fill-opacity="0.4" stroke="#00701a" stroke-width="1" rx="8"/>
  <text x="370" y="265" text-anchor="middle" font-size="16" font-weight="bold">Android Bridge</text>
  <text x="370" y="280" text-anchor="middle" font-size="14">Java + JNI</text>
  <text x="370" y="295" text-anchor="middle" font-size="14">ReactPackage</text>
  <!-- Android JNI Layer -->
  <rect x="295" y="330" width="150" height="60" fill="#3ddc84" fill-opacity="0.4" stroke="#00701a" stroke-width="1" rx="8"/>
  <text x="370" y="350" text-anchor="middle" font-size="16" font-weight="bold">JNI C++ Layer</text>
  <text x="370" y="365" text-anchor="middle" font-size="14">hnsw_jni.cpp</text>
  <text x="370" y="380" text-anchor="middle" font-size="14">libhnsw_native.so</text>
  <!-- Android Static Library -->
  <rect x="295" y="415" width="150" height="50" fill="#3ddc84" fill-opacity="0.2" stroke="#00701a" stroke-width="1" rx="8"/>
  <text x="370" y="438" text-anchor="middle" font-size="16" font-weight="bold">Static Rust Library</text>
  <text x="370" y="453" text-anchor="middle" font-size="14">libhnsw_lib.a</text>
  <!-- Rust Core -->
  <rect x="85" y="510" width="350" height="80" fill="#ce422b" fill-opacity="0.2" stroke="#b71c1c" stroke-width="2" rx="10"/>
  <text x="260" y="538" text-anchor="middle" font-size="22" font-weight="bold">RUST HNSW CORE</text>
  <text x="260" y="558" text-anchor="middle" font-size="16">hnsw_search() • Cosine distance • File I/O</text>
  <text x="260" y="578" text-anchor="middle" font-size="16">extern "C" FFI • Memory-safe • Cross-platform</text>
  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
    <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#0051d5" />
    </marker>
    <marker id="arrowhead-green" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#00701a" />
    </marker>
  </defs>
  <!-- Bridge to platforms -->
  <path d="M 175 140 L 150 180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 345 140 L 370 180" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- iOS flow -->
  <path d="M 150 305 L 150 330" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 150 390 L 150 415" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 150 465 L 170 510" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Android flow -->
  <path d="M 370 305 L 370 330" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 370 390 L 370 415" stroke="#333" stroke-width="1" marker-end="url(#arrowhead)"/>
  <path d="M 370 465 L 350 510" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
</svg>
