``` mermaid
graph LR
    %% React Native Layer
    ReactNative[React Native App]

    %% Bridge Layer
    Bridge[Native Bridge]

    %% Native Platforms
    iOS[iOS Modules & Assets]
    Android[Android Modules & Assets]

    %% Rust Core
    Rust[HNSW Rust Core]

    %% Flow
    ReactNative --> Bridge
    Bridge --> iOS --> Rust
    Bridge --> Android --> Rust

    %% Improved Styling
    classDef rn fill:#61dafb,stroke:#20232a,color:#000,font-size:13px
    classDef bridge fill:#ffd54f,stroke:#f57f17,color:#000,font-size:13px
    classDef native fill:#b2dfdb,stroke:#00695c,color:#000,font-size:13px
    classDef rust fill:#ff8a80,stroke:#b71c1c,color:#000,font-size:13px

    class ReactNative rn
    class Bridge bridge
    class iOS,Android native
    class Rust rust
```