
<svg width="970" height="680" viewBox="0 0 970 680" xmlns="http://www.w3.org/2000/svg">
  <style>
    text, tspan {
      font-family: "Times New Roman";
    }
  </style>
  <!-- Background -->
  <rect width="970" height="680" fill="#FFFFFF"/>
  <!-- Python Processing Pipeline -->
  <rect x="50" y="40" width="870" height="160" fill="#4CAF50" fill-opacity="0.1" stroke="#4CAF50" stroke-width="2" rx="10"/>
  <text x="80" y="65" font-size="18" font-weight="bold" fill="#2E7D32">OFFLINE PROCESSING</text>
  <!-- Document Ingestion -->
  <rect x="80" y="80" width="180" height="100" fill="#E8F5E9" stroke="#4CAF50" stroke-width="1" rx="8"/>
  <text x="170" y="105" text-anchor="middle" font-size="16" font-weight="bold">Document Ingestion</text>
  <!-- File types -->
  <rect x="100" y="115" width="40" height="25" fill="#C8E6C9" stroke="#4CAF50" rx="3"/>
  <text x="120" y="133" text-anchor="middle" font-size="12" font-weight="bold">PDF</text>
  <rect x="150" y="115" width="40" height="25" fill="#C8E6C9" stroke="#4CAF50" rx="3"/>
  <text x="170" y="133" text-anchor="middle" font-size="12" font-weight="bold">CSV</text>
  <rect x="200" y="115" width="40" height="25" fill="#C8E6C9" stroke="#4CAF50" rx="3"/>
  <text x="220" y="133" text-anchor="middle" font-size="12" font-weight="bold">JSON</text>
  <rect x="100" y="145" width="40" height="25" fill="#C8E6C9" stroke="#4CAF50" rx="3"/>
  <text x="120" y="163" text-anchor="middle" font-size="12" font-weight="bold">TXT</text>
  <rect x="150" y="145" width="65" height="25" fill="#C8E6C9" stroke="#4CAF50" rx="3"/>
  <text x="182" y="163" text-anchor="middle" font-size="12" font-weight="bold">XLSX</text>
  <!-- Text Chunking -->
  <rect x="290" y="80" width="180" height="100" fill="#E8F5E9" stroke="#4CAF50" stroke-width="1" rx="8"/>
  <text x="380" y="105" text-anchor="middle" font-size="16" font-weight="bold">Intelligent Chunking</text>
  <text x="380" y="125" text-anchor="middle" font-size="13">Size: 1000 chars</text>
  <text x="380" y="140" text-anchor="middle" font-size="13">Overlap: 200 chars</text>
  <text x="380" y="155" text-anchor="middle" font-size="13">Boundary: Sentence-aware</text>
  <text x="380" y="170" text-anchor="middle" font-size="13">Fallback: Word boundaries</text>
  <!-- Embedding Generation -->
  <rect x="500" y="80" width="180" height="100" fill="#E8F5E9" stroke="#4CAF50" stroke-width="1" rx="8"/>
  <text x="590" y="105" text-anchor="middle" font-size="16" font-weight="bold">Embedding Generation</text>
  <text x="590" y="125" text-anchor="middle" font-size="13">Model: MiniLM-L6-v2</text>
  <text x="590" y="140" text-anchor="middle" font-size="13">Dimensions: 384</text>
  <text x="590" y="155" text-anchor="middle" font-size="13">Format: Float32</text>
  <text x="590" y="170" text-anchor="middle" font-size="13">Normalization: L2</text>
  <!-- Rust Index Building -->
  <rect x="710" y="80" width="180" height="100" fill="#E8F5E9" stroke="#4CAF50" stroke-width="1" rx="8"/>
  <text x="800" y="105" text-anchor="middle" font-size="16" font-weight="bold">HNSW Index Building</text>
  <text x="800" y="125" text-anchor="middle" font-size="13">Rust CLI (main.rs)</text>
  <text x="800" y="140" text-anchor="middle" font-size="13">M=16, ef_construction=200</text>
  <text x="800" y="155" text-anchor="middle" font-size="13">Distance: Cosine</text>
  <text x="800" y="170" text-anchor="middle" font-size="13">Output: .data + .graph</text>
  <!-- Storage Architecture -->
  <rect x="50" y="230" width="870" height="200" fill="#FF9800" fill-opacity="0.1" stroke="#FF9800" stroke-width="2" rx="10"/>
  <text x="80" y="255" font-size="18" font-weight="bold" fill="#E65100">STORAGE ARCHITECTURE</text>
  <!-- Chunked Metadata System -->
  <rect x="80" y="270" width="290" height="140" fill="#FFF3E0" stroke="#FF9800" stroke-width="1" rx="8"/>
  <text x="225" y="295" text-anchor="middle" font-size="16" font-weight="bold">Chunked Metadata System</text>
  <!-- Metadata Index -->
  <rect x="100" y="305" width="120" height="65" fill="#FFE0B2" stroke="#FF9800" rx="5"/>
  <text x="160" y="325" text-anchor="middle" font-size="13" font-weight="bold">metadata_index.json</text>
  <text x="160" y="340" text-anchor="middle" font-size="12">• total_documents</text>
  <text x="160" y="352" text-anchor="middle" font-size="12">• file mappings</text>
  <!-- Metadata Chunks -->
  <rect x="230" y="305" width="120" height="65" fill="#FFE0B2" stroke="#FF9800" rx="5"/>
  <text x="290" y="325" text-anchor="middle" font-size="13" font-weight="bold">Chunk Files</text>
  <text x="290" y="340" text-anchor="middle" font-size="12">metadata_0000.json</text>
  <text x="290" y="352" text-anchor="middle" font-size="12">metadata_0001.json</text>
  <text x="290" y="364" text-anchor="middle" font-size="12">...</text>
  <!-- File size optimization -->
  <rect x="100" y="380" width="250" height="20" fill="#FFCC02" stroke="#FF9800" rx="3"/>
  <text x="225" y="395" text-anchor="middle" font-size="12" font-weight="bold">Target: 5MB per file, 1000 docs per chunk</text>
  <!-- HNSW Index Files -->
  <rect x="390" y="270" width="250" height="140" fill="#FFF3E0" stroke="#FF9800" stroke-width="1" rx="8"/>
  <text x="510" y="295" text-anchor="middle" font-size="16" font-weight="bold">HNSW Index Files</text>
  <!-- Data File -->
  <rect x="410" y="315" width="100" height="75" fill="#FFE0B2" stroke="#FF9800" rx="5"/>
  <text x="460" y="335" text-anchor="middle" font-size="13" font-weight="bold">.hnsw.data</text>
  <text x="460" y="350" text-anchor="middle" font-size="12">Vector storage</text>
  <text x="460" y="362" text-anchor="middle" font-size="12">Node metadata</text>
  <text x="460" y="374" text-anchor="middle" font-size="12">Memory-mapped</text>
  <!-- Graph File -->
  <rect x="520" y="315" width="100" height="75" fill="#FFE0B2" stroke="#FF9800" rx="5"/>
  <text x="570" y="335" text-anchor="middle" font-size="13" font-weight="bold">.hnsw.graph</text>
  <text x="570" y="350" text-anchor="middle" font-size="12">Graph topology</text>
  <text x="570" y="362" text-anchor="middle" font-size="12">Layer structure</text>
  <text x="570" y="374" text-anchor="middle" font-size="12">Neighbor links</text>
  <!-- Performance note -->
  
  <!-- Mobile Asset Distribution -->
  <rect x="660" y="270" width="230" height="140" fill="#FFF3E0" stroke="#FF9800" stroke-width="1" rx="8"/>
  <text x="775" y="295" text-anchor="middle" font-size="16" font-weight="bold">Mobile Asset Distribution</text>
  <!-- Platform assets -->
  <rect x="680" y="310" width="80" height="30" fill="#FFE0B2" stroke="#FF9800" rx="5"/>
  <text x="720" y="330" text-anchor="middle" font-size="13" font-weight="bold">iOS Assets</text>
  <rect x="770" y="310" width="100" height="30" fill="#FFE0B2" stroke="#FF9800" rx="5"/>
  <text x="820" y="330" text-anchor="middle" font-size="13" font-weight="bold">Android Assets</text>
  <!-- Copy process -->
  <rect x="680" y="350" width="190" height="50" fill="#FFCC02" stroke="#FF9800" rx="5"/>
  <text x="775" y="365" text-anchor="middle" font-size="12" font-weight="bold">Automated Copy Process</text>
  <text x="775" y="380" text-anchor="middle" font-size="12">Core files + all metadata chunks</text>
  <text x="777" y="392" text-anchor="middle" font-size="12">to Platform-specific asset dirs</text>
  <!-- Runtime Memory Management -->
  <rect x="50" y="460" width="870" height="180" fill="#2196F3" fill-opacity="0.1" stroke="#2196F3" stroke-width="2" rx="10"/>
  <text x="80" y="485" font-size="18" font-weight="bold" fill="#0D47A1">RUNTIME MEMORY MANAGEMENT</text>
  <!-- LRU Cache System -->
  <rect x="80" y="500" width="270" height="120" fill="#E3F2FD" stroke="#2196F3" stroke-width="1" rx="8"/>
  <text x="210" y="525" text-anchor="middle" font-size="16" font-weight="bold">LRU Cache System</text>
  <!-- Cache components -->
  <rect x="100" y="540" width="80" height="35" fill="#BBDEFB" stroke="#2196F3" rx="5"/>
  <text x="140" y="555" text-anchor="middle" font-size="12" font-weight="bold">Chunk 0</text>
  <text x="140" y="568" text-anchor="middle" font-size="12">1000 docs</text>
  <rect x="190" y="540" width="80" height="35" fill="#BBDEFB" stroke="#2196F3" rx="5"/>
  <text x="230" y="555" text-anchor="middle" font-size="12" font-weight="bold">Chunk 1</text>
  <text x="230" y="568" text-anchor="middle" font-size="12">1000 docs</text>
  <rect x="280" y="540" width="50" height="35" fill="#90CAF9" stroke="#2196F3" rx="5"/>
  <text x="305" y="560" text-anchor="middle" font-size="12" font-weight="bold">...</text>
  <!-- Cache stats -->
  <rect x="100" y="585" width="230" height="25" fill="#64B5F6" stroke="#2196F3" rx="3"/>
  <text x="215" y="602" text-anchor="middle" font-size="12" font-weight="bold">Max Size: 8 chunks (~40MB), Auto-evict</text>
  <!-- Loading Strategy -->
  <rect x="370" y="500" width="270" height="120" fill="#E3F2FD" stroke="#2196F3" stroke-width="1" rx="8"/>
  <text x="500" y="525" text-anchor="middle" font-size="16" font-weight="bold">Intelligent Loading</text>
  <!-- Loading process -->
  <rect x="390" y="540" width="100" height="30" fill="#BBDEFB" stroke="#2196F3" rx="5"/>
  <text x="440" y="560" text-anchor="middle" font-size="12" font-weight="bold">Document ID</text>
  <rect x="520" y="540" width="100" height="30" fill="#BBDEFB" stroke="#2196F3" rx="5"/>
  <text x="570" y="560" text-anchor="middle" font-size="12" font-weight="bold">Chunk Index</text>
  <!-- Formula -->
  <rect x="390" y="580" width="230" height="30" fill="#64B5F6" stroke="#2196F3" rx="3"/>
  <text x="505" y="600" text-anchor="middle" font-size="12" font-weight="bold">chunk = floor(docID / 1000)</text>
  <!-- Performance Metrics -->
  <rect x="660" y="500" width="230" height="120" fill="#E3F2FD" stroke="#2196F3" stroke-width="1" rx="8"/>
  <text x="775" y="525" text-anchor="middle" font-size="16" font-weight="bold">Performance Metrics</text>
  <!-- Metrics list -->
  <rect x="680" y="540" width="190" height="70" fill="#BBDEFB" stroke="#2196F3" rx="5"/>
  <text x="775" y="555" text-anchor="middle" font-size="12" font-weight="bold">Cache Hit Rate: ~85%</text>
  <text x="775" y="570" text-anchor="middle" font-size="12" font-weight="bold">Load Time: ~2-5ms</text>
  <text x="775" y="585" text-anchor="middle" font-size="12" font-weight="bold">Memory Usage: 5MB/chunk</text>
  <text x="775" y="600" text-anchor="middle" font-size="12" font-weight="bold">Prefetch: 3 chunks on init</text>
  <!-- Arrows -->
  <defs>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="10" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>
  <!-- Processing flow arrows -->
  <path d="M 260 130 L 290 130" stroke="#4CAF50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 470 130 L 500 130" stroke="#4CAF50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 680 130 L 710 130" stroke="#4CAF50" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- To storage -->
  <path d="M 380 180 L 380 230" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 590 180 L 590 230" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <path d="M 800 180 L 800 230" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- To runtime -->
  <path d="M 500 430 L 500 460" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <!-- Cache internal flow -->
  <path d="M 490 555 L 520 555" stroke="#2196F3" stroke-width="1" marker-end="url(#arrowhead)"/>
</svg>
