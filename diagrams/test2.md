``` mermaid
graph LR
    %% Bridge layer
    Bridge[Native Bridge]

    %% iOS Specific
    subgraph "🍎 iOS Native"
        iOSBridge[iOS Bridge]
        iOSLib[Static Library]
        iOSAssets[Assets Bundle]
    end

    %% Android Specific
    subgraph "🤖 Android Native"
        AndroidBridge[Android Bridge]
        AndroidLib[Shared Library]
        AndroidAssets[Assets Bundle]
    end

    %% Rust Core
    Rust[HNSW Rust Core]

    %% Connections
    Bridge --> iOSBridge
    Bridge --> AndroidBridge

    iOSBridge --> iOSLib --> Rust
    AndroidBridge --> AndroidLib --> Rust

    Bridge --> iOSAssets
    Bridge --> AndroidAssets

    %% Styling
    classDef bridge fill:#ffd54f,stroke:#f57f17,stroke-width:2px
    classDef ios fill:#007aff,stroke:#0051d5,stroke-width:2px,color:white
    classDef android fill:#3ddc84,stroke:#00701a,stroke-width:2px
    classDef rust fill:#ff8a80,stroke:#b71c1c,stroke-width:2px

    class Bridge bridge
    class iOSBridge,iOSLib,iOSAssets ios
    class AndroidBridge,AndroidLib,AndroidAssets android
    class Rust rust
```